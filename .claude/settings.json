{"hooks": {"Notification": [{"matcher": "", "hooks": [{"type": "command", "command": "afplay $CLAUDE_PROJECT_DIR/sounds/waiting_orders.wav"}, {"type": "command", "command": "osascript -e 'display notification \"Waiting orders!!!\" with title \"Claude Code\"'"}]}], "Stop": [{"matcher": "", "hooks": [{"type": "command", "command": "afplay $CLAUDE_PROJECT_DIR/sounds/mission_accomplished.mp3"}]}], "PostToolUse": [{"matcher": "Edit|MultiEdit|Write", "hooks": [{"type": "command", "command": "jq -r  '.tool_input.file_path' | xargs fvm dart format"}]}]}}