{"permissions": {"allow": ["<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(sed:*)", "Bash(cp:*)", "<PERSON><PERSON>(fvm flutter doctor:*)", "Bash(fvm flutter:*)", "<PERSON><PERSON>(git worktree:*)", "Bash(git checkout:*)", "Bash(git merge:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git branch:*)", "<PERSON><PERSON>(rmdir:*)", "mcp__context7__get-library-docs", "Bash(find:*)", "WebFetch(domain:www.figma.com)", "mcp__<PERSON><PERSON><PERSON>_Figma_MCP__download_figma_images", "mcp__<PERSON><PERSON><PERSON>_Figma_MCP__get_figma_data"], "deny": [], "ask": []}, "outputStyle": "Explanatory"}