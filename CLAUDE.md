# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 提供在此代码库中工作的指导。

## 项目概述

这是一个名为 `round_table` 的 Flutter 移动应用项目 - 目前是一个基础的计数器演示应用。该项目使用 Flutter SDK 3.6.2+，面向 Android 和 iOS 平台。

## 开发命令

### Flutter 命令（根据全局指示使用 FVM 前缀）
```bash
# 运行应用
fvm flutter run

# 运行测试
fvm flutter test

# 运行特定测试文件
fvm flutter test test/widget_test.dart

# 分析代码问题
fvm flutter analyze

# 获取依赖包
fvm flutter pub get

# 升级依赖包
fvm flutter pub upgrade

# 构建生产版本
fvm flutter build apk        # Android
fvm flutter build ios        # iOS
fvm flutter build web        # Web (如果已配置)
```

### 开发工作流程
```bash
# 清理构建产物
fvm flutter clean

# 检查环境问题的医生诊断
fvm flutter doctor

# 格式化代码
fvm dart format .

# 检查过时的包
fvm flutter pub outdated
```

## 代码架构

### 当前结构
- **lib/main.dart**: 标准 Flutter 计数器应用的入口点实现
  - `MyApp`: 根 StatelessWidget，包含 MaterialApp 配置
  - `MyHomePage`: StatefulWidget，管理计数器状态
  - 使用 `setState()` 进行状态管理（基础 Flutter 模式）
  - Material Design 3 主题，采用 deepPurple 配色方案

### Dart 偏好设置（根据全局指示）
- 始终使用尾随逗号，以获得更好的格式化和 git diff 效果
- 对于不会被重新赋值的变量，优先使用 `final` 而不是 `var`
- 遵循标准的 Flutter/Dart 命名约定

### 测试
- **test/widget_test.dart**: 计数器功能的基础部件测试
- 使用 `flutter_test` 包
- 测试模式：构建部件 → 交互 → 验证状态变化

## 关键依赖
- **flutter**: 核心 Flutter 框架
- **cupertino_icons**: iOS 风格图标
- **flutter_test**: 测试框架（开发依赖）
- **flutter_lints**: Dart/Flutter 代码检查规则（开发依赖）

## 平台配置
- **Android**: `android/` 目录中的完整 Android 项目
- **iOS**: `ios/` 目录中的完整 iOS 项目
- **Material Design**: 通过 `uses-material-design: true` 启用

## 代码质量
- 通过 `analysis_options.yaml` 使用 `flutter_lints` 配置代码检查
- 启用了标准的 Flutter 代码检查规则
- 可根据项目需求自定义分析器

## 项目状态
这是一个新创建的 Flutter 项目模板。当前实现是一个基础的计数器演示，可作为更复杂移动应用的基础。

## 实现规范
- 获取网络数据的实现规范定义在 @docs/network_spec.md 中。
- 存储键值对数据的实现规范定义在 @docs/lightweight_storage_spec.md 中。
- 应用全局统一的设计规范定义在 @docs/design_spec.md 中。