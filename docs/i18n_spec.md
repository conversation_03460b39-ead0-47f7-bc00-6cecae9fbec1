## Flutter 国际化 (i18n) 模块实现规范

### 1\. 概述

#### 1.1. 目标

* **快速实现**: 采用 Flutter 官方推荐的自动化方案，最大限度地减少手动编码和配置工作。
* **类型安全**: 生成的所有本地化字符串都将是类型安全的，在编译时即可发现拼写错误，杜绝运行时因 Key 错误导致的崩溃。
* **易于维护与扩展**: 语言资源文件（`.arb`）格式清晰，易于管理和交付给翻译人员。未来添加新语言或修改文案非常方便。
* **支持 RTL**: 无缝支持阿拉伯语等从右到左 (RTL) 的语言布局。

#### 1.2. 技术选型

* **核心框架**: `flutter_localizations` 结合 Dart 的 `intl` 包。
* **实现方式**: 使用 Flutter SDK 内置的代码生成器。通过编写 `.arb` (Application Resource Bundle) 格式的资源文件和 `l10n.yaml` 配置文件，自动生成本地化所需的 Dart 代码。

**理由**: 这是 Flutter 官方当前最推荐的方案。它避免了大量的手动样板代码，提供了无与伦比的类型安全，并且与 Flutter 构建系统紧密集成，是实现 MVP 的最佳实践。

### 2\. 实现步骤

#### 2.1. 项目设置

1.  **添加依赖**: 在 `pubspec.yaml` 文件中，确保 `flutter_localizations` 已添加为依赖。

    ```yaml
    dependencies:
      flutter:
        sdk: flutter
      flutter_localizations: # <-- 添加此行
        sdk: flutter
      intl: ^0.19.0 # intl的版本号可能会更新，请使用最新版

    flutter:
      uses-material-design: true
      generate: true # <-- 启用代码生成器
    ```

2.  **创建配置文件**: 在项目根目录下（与 `pubspec.yaml` 同级）创建一个名为 `l10n.yaml` 的文件。

    ```yaml
    # l10n.yaml
    arb-dir: lib/l10n # 指定 .arb 资源文件所在的目录
    template-arb-file: app_en.arb # 指定模板文件，通常是英语
    output-localization-file: app_localizations.dart # 指定生成的 Dart 文件名
    nullable-getter: false # 建议设为 false，确保所有 key 都有值
    ```

#### 2.2. 创建语言资源文件

1.  **创建目录**: 根据 `l10n.yaml` 中的配置，在 `lib` 目录下创建一个名为 `l10n` 的文件夹。

2.  **创建 `.arb` 文件**: 在 `lib/l10n` 目录下，为每种需要支持的语言创建一个 `.arb` 文件。文件名必须遵循 `app_<languageCode>.arb` 或 `app_<languageCode>_<scriptCode>.arb` 的格式。

  * **英语 (en)** - `app_en.arb` (模板文件)
  * **繁体中文 (zh\_Hant)** - `app_zh_Hant.arb`
  * **阿拉伯语 (ar)** - `app_ar.arb`
  * **印地语 (hi)** - `app_hi.arb`

    **文件内容示例**:

  * `lib/l10n/app_en.arb` (English)

    ```json
    {
      "@@locale": "en",
      "pageTitle": "Schedule",
      "helloUser": "Hello, {userName}",
      "@helloUser": {
        "description": "A greeting to the user.",
        "placeholders": {
          "userName": {
            "type": "String",
            "example": "Alexander"
          }
        }
      }
    }
    ```

  * `lib/l10n/app_zh_Hant.arb` (Traditional Chinese)

    ```json
    {
      "@@locale": "zh_Hant",
      "pageTitle": "日程",
      "helloUser": "你好，{userName}"
    }
    ```

  * `lib/l10n/app_ar.arb` (Arabic)

    ```json
    {
      "@@locale": "ar",
      "pageTitle": "جدول",
      "helloUser": "مرحباً، {userName}"
    }
    ```

  * `lib/l10n/app_hi.arb` (Hindi)

    ```json
    {
      "@@locale": "hi",
      "pageTitle": "अनुसूची",
      "helloUser": "नमस्ते, {userName}"
    }
    ```

    > **注意**: `@` 符号开头的键是元数据，用于为翻译人员提供上下文信息，不会生成代码。带参数的字符串 (`{userName}`) 格式非常重要。

#### 2.3. 集成到 Flutter App

1.  **生成代码**: 当您保存 `.arb` 文件或构建项目时，Flutter 会自动在 `.dart_tool/flutter_gen/gen_l10n` 目录下生成本地化代码。如果未自动生成，可以手动运行 `flutter gen-l10n`。

2.  **配置 `MaterialApp`**: 在 `lib/main.dart` 中，修改您的 `MaterialApp` 以支持多语言。

    ```dart
    import 'package:flutter/material.dart';
    import 'package:flutter_gen/gen_l10n/app_localizations.dart'; // 导入生成的类

    void main() {
      runApp(const MyApp());
    }

    class MyApp extends StatelessWidget {
      const MyApp({super.key});

      @override
      Widget build(BuildContext context) {
        return MaterialApp(
          title: 'Your App Name',
          // 1. 添加本地化代理
          localizationsDelegates: AppLocalizations.localizationsDelegates,
          // 2. 声明支持的语言
          supportedLocales: AppLocalizations.supportedLocales,
          // ... 其他 MaterialApp 配置
          home: YourHomePage(),
        );
      }
    }
    ```

#### 2.4. 在 Widget 中使用本地化字符串

现在，您可以在任何 Widget 中通过 `context` 安全地访问本地化字符串。

```dart
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class YourHomePage extends StatelessWidget {
  const YourHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取当前语言环境的 AppLocalizations 实例
    final l10n = AppLocalizations.of(context)!;
    final String userName = "Alex"; // 示例用户名

    return Scaffold(
      appBar: AppBar(
        // 类型安全地访问 'pageTitle'
        title: Text(l10n.pageTitle),
      ),
      body: Center(
        // 类型安全地访问带参数的 'helloUser'
        child: Text(l10n.helloUser(userName)),
      ),
    );
  }
}
```

### 3\. 特殊注意事项：RTL 语言（阿拉伯语）

Flutter 对 RTL 布局有出色的内置支持。当用户系统语言或您指定的 `Locale` 是阿拉伯语 (`ar`) 时，`MaterialApp` 会**自动**切换整个应用的布局方向。