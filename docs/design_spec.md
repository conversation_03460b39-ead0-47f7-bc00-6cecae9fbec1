## Flutter App 设计规范文档

### 1\. 概述

#### 1.1. 设计原则

* **简洁清晰 (Clean & Clear)**: 界面元素保持简洁，信息层级分明，避免不必要的装饰。
* **柔和友好 (Soft & Friendly)**: 大量使用圆角、柔和的色彩和阴影，营造亲切、现代的用户体验。
* **组件化 (Component-Driven)**: 将重复出现的 UI 元素（如会议卡片、按钮）抽象为可复用的 Flutter Widget，提高开发效率和一致性。

### 2\. 色彩规范 (Color Palette)

色彩是 App 的基调。我们将定义一套核心色板，并在 Flutter 的 `ThemeData` 中进行配置。

| 用途                   | 颜色名称          | 十六进制 (Hex) | Flutter `Color`                       | 备注                               |
| ---------------------- | ----------------- | -------------- | ------------------------------------- | ---------------------------------- |
| **主背景色** | `background`      | `#F8F9FA`      | `Color(0xFFF8F9FA)`                   | 应用所有页面的主背景色             |
| **组件/卡片背景色** | `surface`         | `#FFFFFF`      | `Colors.white`                        | 卡片、对话框、底部导航栏的背景     |
| **主要品牌/强调色** | `primary`         | `#FF9F43`      | `Color(0xFFFF9F43)`                   | 用于关键统计数据、图标等           |
| **主要文本/标题色** | `textPrimary`     | `#1E1E1E`      | `Color(0xFF1E1E1E)`                   | 页面标题、卡片标题等重要文字       |
| **次要文本色** | `textSecondary`   | `#8A8A8E`      | `Color(0xFF8A8A8E)`                   | 描述、时间、邮件等辅助性文字       |
| **按钮/深色元素** | `dark`            | `#2C2C2E`      | `Color(0xFF2C2C2E)`                   | 用于主要按钮背景、选中的日期       |
| **边框/分割线** | `border`          | `#EFEFEF`      | `Color(0xFFEFEFEF)`                   | 轻微的边框或分割线                 |
| **成功/在线状态** | `success`         | `#34C759`      | `Color(0xFF34C759)`                   | -                                  |

#### 卡片柔和色板 (Pastel Palette for Cards)

这是设计中的一个亮点，用于区分不同的事件卡片。

| 颜色名称    | 十六进制 (Hex) | Flutter `Color`     |
| ----------- | -------------- | ------------------- |
| `pastelGreen` | `#E0F5F2`      | `Color(0xFFE0F5F2)` |
| `pastelYellow`| `#FFF6E5`      | `Color(0xFFFFF6E5)` |
| `pastelPurple`| `#EAE6FF`      | `Color(0xFFEAE6FF)` |
| `pastelBlue`  | `#DDF1FF`      | `Color(0xFFDDF1FF)` |

### 3\. 字体与排版 (Typography)

统一的字体和字号能带来稳定、专业的视觉感受。我们选用 Google Fonts 中的 `Poppins` 字体，它现代、圆润，非常符合整体设计风格。

**集成方式**: 使用 `google_fonts` 包。

#### 文本样式定义 (Text Theme)

| 用途             | Flutter `TextStyle` | 字体 (`Poppins`) | 字号 (Size) | 字重 (Weight) | 颜色             |
| ---------------- | ------------------- | ---------------- | ----------- | ------------- | ---------------- |
| **页面大标题** | `headlineLarge`     | Poppins          | 28.0        | `FontWeight.w600` | `textPrimary`    |
| **问候语/副标题**| `headlineMedium`    | Poppins          | 22.0        | `FontWeight.w500` | `textPrimary`    |
| **卡片/列表标题**| `titleLarge`        | Poppins          | 17.0        | `FontWeight.w500` | `textPrimary`    |
| **正文/主要内容**| `bodyLarge`         | Poppins          | 15.0        | `FontWeight.w400` | `textPrimary`    |
| **次要信息/描述**| `bodyMedium`        | Poppins          | 14.0        | `FontWeight.w400` | `textSecondary`  |
| **按钮文字** | `labelLarge`        | Poppins          | 16.0        | `FontWeight.w500` | `Colors.white`   |

### 4\. 布局与间距 (Layout & Spacing)

统一的间距单位是确保布局呼吸感和一致性的关键。

* **基础间距单位**: **8.0**
* **常用间距**:
  * `xxs`: 2.0 (极小间距)
  * `xs`: 4.0 (图标与文字)
  * `sm`: 8.0 (元素内边距)
  * `md`: 16.0 (组件之间的标准间距)
  * `lg`: 24.0 (区块之间的间距)
  * `xl`: 32.0 (大区块间距)
* **页面边距 (Screen Padding)**: 所有页面的主要内容应设置水平方向 `20.0` 的内边距。
  ```dart
  Padding(
    padding: const EdgeInsets.symmetric(horizontal: 20.0),
    // ... child
  )
  ```

### 5\. 核心组件规范 (Core Component Specs)

以下是根据设计图提炼出的核心组件及其 Flutter 实现规范。

#### 5.1. 卡片 (Card)

这是最核心的组件，用于展示会议、任务等信息。

* **实现方式**: 使用 `Container` + `BoxDecoration` 或 `Card` Widget。
* **圆角 (`borderRadius`)**: `BorderRadius.circular(20.0)`
* **阴影 (`boxShadow`)**: 使用柔和、弥散的阴影。
  ```dart
  BoxShadow(
    color: Colors.black.withOpacity(0.05),
    blurRadius: 20,
    offset: const Offset(0, 5),
  )
  ```
* **内边距 (`padding`)**: `EdgeInsets.all(16.0)`
* **背景色**: 从“卡片柔和色板”中选取。

#### 5.2. 按钮 (Button)

* **主要按钮 (Primary Button)**: 例如 "Open Meeting"
  * **实现方式**: `ElevatedButton`
  * **样式 (`style`)**:
    * `backgroundColor`: `dark` (`#2C2C2E`)
    * `foregroundColor`: `Colors.white`
    * `shape`: `RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.0))`
    * `padding`: `EdgeInsets.symmetric(vertical: 16.0)`
* **图标按钮 (Icon Button)**
  * **实现方式**: `IconButton`
  * **样式**:
    * `iconSize`: 24.0
    * `color`: `textSecondary`

#### 5.3. 日期选择器 (Date Selector)

一个水平滚动的日期条。

* **实现方式**: 使用 `ListView.builder`，设置 `scrollDirection: Axis.horizontal`。
* **单个日期项 (Item)**:
  * **未选中**: `Container`，无背景色，文字颜色为 `textSecondary`。
  * **选中**: `Container`，背景色为 `dark` (`#2C2C2E`)，文字颜色为 `Colors.white`。
  * **尺寸**: 建议固定宽高，如 `width: 48`, `height: 64`。
  * **圆角**: `BorderRadius.circular(16.0)`。

#### 5.4. 底部导航栏 (Bottom Navigation Bar)

* **实现方式**: `BottomNavigationBar`
* **样式**:
  * `backgroundColor`: `surface` (`#FFFFFF`)
  * `elevation`: 0 (建议使用 `Container` 包裹并添加自定义阴影，以实现设计图中的柔和效果)。
  * `selectedItemColor`: `primary` (`#FF9F43`)
  * `unselectedItemColor`: `textSecondary`
  * `showSelectedLabels`: `false` (不显示文字)
  * `showUnselectedLabels`: `false`

#### 5.5. 应用栏 (App Bar)

* **实现方式**: `AppBar`
* **样式**:
  * `backgroundColor`: `background` (`#F8F9FA`)
  * `elevation`: 0 (无阴影)
  * `titleTextStyle`: 使用 `titleLarge` 样式
  * `iconTheme`: `IconThemeData(color: textPrimary)`

### 6\. 图标 (Iconography)

设计图中的图标为简约的线性图标。

* **图标库推荐**:
  * **优先**: `Iconsax` 或 `Feather Icons` (可通过 `iconsax` 或 `feather_icons_flutter` 包引入)。
  * **备选**: Flutter 内置的 Material Icons (`Icons.`)，选择其 outlined 版本。
* **规范**:
  * **默认颜色**: `textSecondary`
  * **激活/选中颜色**: `primary`
  * **标准尺寸**: 24.0
