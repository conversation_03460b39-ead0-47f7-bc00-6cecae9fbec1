## Flutter 网络模块实现规范

### 1\. 概述

#### 1.1. 目标

* **快速实现**: 建立一套简洁高效的网络请求机制，满足 MVP 阶段的业务需求。
* **标准化**: 统一网络请求、响应处理、错误处理和数据模型的格式。
* **可维护性**: 结构清晰，便于调试、修改和跟踪问题。
* **可扩展性**: 易于在未来版本中添加新功能，如缓存、请求重试、无感刷新 Token 等。

#### 1.2. 技术选型

为了平衡开发效率和功能性，我们选择以下核心库：

* **HTTP 客户端**: `dio`
  * **理由**: 相比官方的 `http` 包，`dio` 提供了更强大的功能，如拦截器 (Interceptors)、全局配置、请求取消、文件下载等。拦截器对于日志记录和统一认证处理尤为重要，能极大简化 MVP 开发。
* **JSON 序列化**: `json_serializable` + `freezed` (可选但强烈推荐)
  * **理由**: `json_serializable` 能够自动生成 `fromJson`/`toJson` 的模板代码，避免手动解析 JSON，减少出错概率。结合 `freezed` 可以创建不可变的数据模型 (Model)，并自动处理 `copyWith`、`toString` 等方法，提升代码的健壮性和可读性。
* **服务定位/依赖注入**: `get_it`
  * **理由**: 一个简单、快速的服务定位器，用于解耦代码。通过 `get_it`，我们可以轻松地在应用的任何地方获取网络客户端或 Repository 的单例。

### 2\. 架构设计

我们将采用分层架构来隔离职责，使代码更清晰。

```
+---------------------------+
|        UI / Widget        |  (表示层)
+---------------------------+
             |
             v
+---------------------------+
|  State Management (BLoC/  |  (业务逻辑层)
|    Riverpod/Provider)     |
+---------------------------+
             |
             v
+---------------------------+
|    Repository Layer       |  (数据仓库层)
+---------------------------+
             |
             v
+---------------------------+
|      API Service Layer    |  (API 服务层)
+---------------------------+
             |
             v
+---------------------------+
|    HTTP Client (Dio)      |  (网络客户端层)
+---------------------------+
```

* **HTTP Client**: 封装 `dio` 的实例和全局配置。
* **API Service**: 定义具体的 API 接口，如 `getUsers()`、`createPost(data)`。
* **Repository Layer**: 作为业务逻辑层和数据源之间的桥梁。在 MVP 阶段，它可以直接调用 API Service；未来可以扩展为决策是从网络还是本地缓存获取数据。
* **State Management**: 调用 Repository 的方法，并根据返回结果（加载中、成功、失败）更新 UI。

### 3\. 实现规范

#### 3.1. 目录结构

建议将网络相关代码组织在以下目录结构中：

```
lib/
└── core/
|   └── network/
|       ├── dio_client.dart       # Dio 实例和配置
|       └── interceptors/
|           ├── auth_interceptor.dart   # 认证拦截器
|           └── logging_interceptor.dart# 日志拦截器
|
└── data/
    ├── models/                 # 数据模型 (使用 freezed & json_serializable)
    |   ├── user.dart
    |   └── user.g.dart
    |   └── user.freezed.dart
    |
    ├── repositories/           # Repository 实现
    |   └── user_repository.dart
    |
    └── services/               # API 服务定义
        └── user_api_service.dart
```

#### 3.2. HTTP Client (Dio) 封装

创建一个单例的 Dio Client，用于管理基础配置和拦截器。

**路径**: `lib/core/network/dio_client.dart`

**要求**:

* **基础配置 (`BaseOptions`)**:
  * `baseUrl`: 从环境变量或配置文件中读取，区分开发、测试和生产环境。
  * `connectTimeout`: 设置连接超时时间 (建议 5-10 秒)。
  * `receiveTimeout`: 设置接收超时时间 (建议 10-15 秒)。
  * `headers`: 设置全局 Header，如 `Content-Type: application/json`。
* **单例模式**: 使用 `get_it` 注册为单例，确保应用中只有一个 Dio 实例。
* **拦截器 (`Interceptors`)**:
  * **日志拦截器**: 在 Debug 模式下打印完整的请求和响应信息（URL、Header、Body、状态码），便于调试。
  * **认证拦截器**: 统一处理认证逻辑。从本地存储（如 SharedPreferences）读取 Token，并将其添加到每个请求的 Header 中 (`Authorization: Bearer <token>`)。

**示例代码**:

```dart
// lib/core/network/dio_client.dart
import 'package:dio/dio.dart';
import 'interceptors/auth_interceptor.dart';
import 'interceptors/logging_interceptor.dart';

class DioClient {
  final Dio _dio;

  DioClient() : _dio = Dio() {
    _dio.options = BaseOptions(
      baseUrl: 'https://api.yourapp.com/v1/', // 从配置读取
      connectTimeout: Duration(seconds: 10),
      receiveTimeout: Duration(seconds: 15),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    // 只在 Debug 模式下添加日志拦截器
    assert(() {
      _dio.interceptors.add(LoggingInterceptor());
      return true;
    }());
    
    _dio.interceptors.add(AuthInterceptor());
  }

  Dio get instance => _dio;
}

// 使用 get_it 注册
// final getIt = GetIt.instance;
// getIt.registerSingleton<DioClient>(DioClient());
// Dio dio = getIt<DioClient>().instance;
```

#### 3.3. 数据模型 (Model)

**要求**:

* 所有 API 响应体都必须有对应的 Model 类。
* 使用 `@freezed` 和 `@JsonSerializable` 注解来自动生成代码。
* 字段名使用驼峰式命名 (`camelCase`)，如果 API 返回的是蛇形命名 (`snake_case`)，使用 `@JsonKey(name: 'snake_case_name')` 进行映射。

**示例代码**:

```dart
// lib/data/models/user.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
class User with _$User {
  const factory User({
    required int id,
    required String name,
    required String email,
    @JsonKey(name: 'created_at') required DateTime createdAt,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}
```

#### 3.4. API Service

**要求**:

* 根据业务模块或 API 资源对 Service 进行划分（如 `UserApiService`, `ProductApiService`）。
* 每个 Service 接收一个 `Dio` 实例作为依赖。
* 方法返回类型应为 `Future<Model>` 或 `Future<List<Model>>`。
* 方法应清晰地反映其功能，如 `Future<User> getUserById(String id);`。

**示例代码**:

```dart
// lib/data/services/user_api_service.dart
import 'package:dio/dio.dart';
import '../models/user.dart';

class UserApiService {
  final Dio _dio;

  UserApiService(this._dio);

  Future<User> getUserById(String id) async {
    try {
      final response = await _dio.get('/users/$id');
      return User.fromJson(response.data);
    } on DioException {
        // 异常将在 Repository 层或更高层级处理
        rethrow;
    }
  }

  Future<List<User>> getAllUsers() async {
      try {
        final response = await _dio.get('/users');
        final List<dynamic> userListJson = response.data['data']; // 假设数据在 data 字段下
        return userListJson.map((json) => User.fromJson(json)).toList();
      } on DioException {
          rethrow;
      }
  }
}
```

#### 3.5. 错误处理

**要求**:

* 定义一个统一的异常类 `ApiException`，用于封装不同类型的网络错误。
* 在 Repository 层捕获 `DioException` 并将其转换为自定义的 `ApiException`。
* `ApiException`应包含错误码和用户可读的错误信息。

**建议的错误类型**:

* `NetworkError`: 无网络连接。
* `ServerError`: 服务器内部错误 (5xx)。
* `ClientError`: 客户端请求错误 (4xx)，如参数错误、未授权。
* `TimeoutError`: 请求超时。
* `UnknownError`: 未知错误。

**示例**:

```dart
// 在 Repository 层中
Future<User> fetchUser(String id) async {
  try {
    return await _userApiService.getUserById(id);
  } on DioException catch (e) {
    // 将 DioException 转换为自定义的业务异常
    if (e.type == DioExceptionType.connectionTimeout || e.type == DioExceptionType.receiveTimeout) {
      throw ApiException(message: "网络超时，请重试");
    }
    if (e.response?.statusCode == 401) {
      throw ApiException(message: "认证失败，请重新登录");
    }
    // ... 其他错误类型转换
    throw ApiException(message: "发生未知错误");
  }
}
```