## Flutter Shared Preferences 轻量存储模块实现规范 (MVP V1.0)

### 1\. 概述

#### 1.1. 目标

* **快速集成**: 提供一个即插即用的本地存储解决方案，如存储用户设置、认证 Token、应用状态等。
* **类型安全**: 通过常量化管理存储键 (Key)，避免直接使用字符串导致的拼写和类型转换错误。
* **代码解耦**: 将存储逻辑完全封装在静态服务类中，使业务代码与具体的存储实现分离。
* **调用简洁**: 提供简单直接的静态方法调用 (`StorageService.saveToken(...)`)，无需管理实例。
* **易于测试**: 尽管使用了静态类，但通过依赖注入等高级技巧，仍可在未来替换为模拟实现。

#### 1.2. 技术选型

* **核心库**: `shared_preferences`
    * **理由**: 这是 Flutter 官方推荐的用于存储简单键值对数据的库。它在 iOS 上使用 `NSUserDefaults`，在 Android 上使用 `SharedPreferences`，性能可靠，非常适合 MVP 阶段的轻量级存储需求。

### 2\. 架构设计

我们将创建一个专门的 `StorageService` 静态工具类来封装所有与 `shared_preferences` 的交互。这个服务将作为应用中所有本地存储操作的唯一、直接的入口。

```
+---------------------------+
|        UI / Widget        |
+---------------------------+
             |
             v
+---------------------------+
|  State Management (BLoC/  |
|    Riverpod/Provider)     |
+---------------------------+
             |
             v
+---------------------------+
|    Repository Layer       |
+---------------------------+
             |
             v
+---------------------------+
|      StorageService       |  <-- 封装 SharedPreferences (静态类)
+---------------------------+
             |
             v
+---------------------------+
|   shared_preferences Plugin |
+---------------------------+
```

* **StorageService**: 一个完全静态的类，负责初始化 `SharedPreferences` 实例，并提供类型安全的静态 `get` 和 `set` 方法。它不能被实例化。
* **Repository Layer**: 业务逻辑的数据仓库，当需要持久化数据时，直接调用 `StorageService` 的静态方法。
* **State Management**: 从 Repository 获取数据，并决定何时需要进行数据持久化。

### 3\. 实现规范

#### 3.1. 目录结构

建议将存储相关代码组织在以下目录结构中：

```
lib/
└── core/
    └── services/
        ├── storage_service.dart     # 核心存储服务 (静态类)
        └── storage_keys.dart      # 存储键的常量定义
```

#### 3.2. 存储键 (Storage Keys) 管理

**要求**:

* **禁止硬编码**: 绝对禁止在代码中直接使用字符串作为 Key (例如：`prefs.getString('user_token')`)。
* **常量化**: 所有的存储键都必须在 `storage_keys.dart` 文件中定义为常量。这可以提供代码提示，并从根本上杜绝拼写错误。

**示例代码**:

```dart
// lib/core/services/storage_keys.dart

class StorageKeys {
  // 私有构造函数，防止该类被实例化
  StorageKeys._();

  // 用户认证 Token
  static const String userToken = 'user_token';

  // 应用主题模式 (例如：'light', 'dark', 'system')
  static const String appTheme = 'app_theme';

  // 是否首次打开应用
  static const String isFirstOpen = 'is_first_open';

  // 用户语言设置 (例如：'en', 'zh')
  static const String userLanguage = 'user_language';
}
```

#### 3.3. StorageService 封装 (静态类实现)

**要求**:

* **完全静态**: 类中的所有成员和方法都必须是 `static` 的。
* **禁止实例化**: 提供一个私有构造函数 `StorageService._()` 来防止外部代码通过 `StorageService()` 创建实例。
* **异步初始化**: `SharedPreferences.getInstance()` 是一个异步操作。`StorageService` 需要一个静态的异步初始化方法 `init()`，并在应用启动时调用。
* **类型安全的方法**: 提供明确的静态 `get` 和 `set` 方法，如 `setString(key, value)`、`getString(key)` 等。
* **提供默认值**: `get` 方法应该支持传入一个可选的默认值，当读取的 Key 不存在时返回该默认值，避免空指针异常。

**示例代码**:

```dart
// lib/core/services/storage_service.dart

import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  // 私有构造函数，防止该类被实例化
  StorageService._();

  static late final SharedPreferences _prefs;

  // 静态的异步初始化方法
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // --- 通用方法 (全部为静态) ---

  static Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }

  static String getString(String key, {String defaultValue = ''}) {
    return _prefs.getString(key) ?? defaultValue;
  }

  static Future<bool> setBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }

  static bool getBool(String key, {bool defaultValue = false}) {
    return _prefs.getBool(key) ?? defaultValue;
  }

  static Future<bool> setInt(String key, int value) async {
    return await _prefs.setInt(key, value);
  }

  static int getInt(String key, {int defaultValue = 0}) {
    return _prefs.getInt(key) ?? defaultValue;
  }

  // --- 清除数据 ---

  static Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }

  static Future<bool> clear() async {
    return await _prefs.clear();
  }
}
```

#### 3.4. 初始化与使用

**要求**:

* 在应用的 `main` 函数中，`runApp` 之前，必须调用 `StorageService.init()` 来完成初始化。
* 所有调用都通过类名直接访问静态方法，例如 `StorageService.getString(...)`。

**示例代码**:

```dart
// lib/main.dart

import 'package:flutter/material.dart';
import 'core/services/storage_service.dart';

void main() async {
  // 确保 Flutter 绑定已初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 调用静态方法进行初始化
  await StorageService.init();

  runApp(const MyApp());
}

// 在应用的任何地方使用
import 'core/services/storage_service.dart';
import 'core/services/storage_keys.dart';

class AuthRepository {
  Future<void> login(String username, String password) async {
    // 假设登录成功，获取到 token
    String token = "your_auth_token";
    
    // 直接调用静态方法保存 token
    await StorageService.setString(StorageKeys.userToken, token);
  }

  String getToken() {
    // 直接调用静态方法读取 token
    return StorageService.getString(StorageKeys.userToken);
  }

  Future<void> logout() async {
    // 直接调用静态方法移除 token
    await StorageService.remove(StorageKeys.userToken);
  }
}
```
