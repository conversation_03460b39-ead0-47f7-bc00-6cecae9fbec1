# SiliconFlow OpenAI API 使用指南

本项目已集成 SiliconFlow OpenAI 聊天补全 API，提供了完整的实现方案。

## 功能特性

✅ **完整的 API 集成**
- 支持 SiliconFlow 平台的所有模型
- 普通聊天补全和流式响应
- 自动聊天记录存储
- API Key 管理和验证

✅ **架构设计**
- 分层架构：API Service → Repository → UI
- 使用 Dio 进行网络请求
- 完整的错误处理机制
- 依赖注入（get_it）

✅ **数据模型**
- 使用 freezed + json_serializable
- 类型安全的请求/响应模型
- 支持流式响应解析

## 快速开始

### 1. 配置 API Key

在应用的设置中添加 SiliconFlow API Key：

```dart
import 'package:round_table/core/services/storage_service.dart';
import 'package:round_table/core/services/storage_keys.dart';

// 保存 API Key
await StorageService.setString(StorageKeys.siliconFlowApiKey, 'your-api-key');
```

### 2. 使用 ChatRepository

```dart
import 'package:round_table/core/services/service_locator.dart';
import 'package:round_table/data/repositories/chat_repository.dart';
import 'package:round_table/data/models/chat_message.dart';

// 获取 ChatRepository 实例
final chatRepository = getIt<ChatRepository>();

// 设置 API Key
chatRepository.setApiKey('your-api-key');

// 创建聊天消息
final messages = [
  ChatMessage(role: 'user', content: '你好，AI助手！'),
];

// 发送聊天请求
final response = await chatRepository.createChatCompletion(
  model: 'Qwen/Qwen3-8B',
  messages: messages,
  maxTokens: 2048,
);

print('AI 回复: $response');
```

### 3. 流式响应

```dart
// 创建流式聊天请求
final stream = chatRepository.createStreamingChatCompletion(
  model: 'Qwen/Qwen3-8B',
  messages: messages,
  maxTokens: 2048,
);

// 处理流式响应
await for (final chunk in stream) {
  print(chunk); // 逐步输出 AI 回复
}
```

### 4. 获取可用模型

```dart
// 获取可用模型列表
final models = await chatRepository.getAvailableModels();
print('可用模型: $models');
```

### 5. 管理聊天记录

```dart
// 获取聊天历史
final history = await chatRepository.getChatHistory();

// 清除聊天记录
await chatRepository.clearChatHistory();
```

## 支持的模型

- `Qwen/Qwen3-8B` - 推荐的基础模型
- `deepseek-ai/DeepSeek-R1` - 支持推理的模型
- `Qwen/Qwen2-VL-72B-Instruct` - 多模态模型

## 错误处理

API 提供了完整的错误处理机制：

```dart
try {
  final response = await chatRepository.createChatCompletion(
    model: 'Qwen/Qwen3-8B',
    messages: messages,
  );
} on NetworkError catch (e) {
  // 网络连接错误
  print('网络错误: ${e.message}');
} on UnauthorizedError catch (e) {
  // API Key 无效
  print('认证错误: ${e.message}');
} on ServerError catch (e) {
  // 服务器错误
  print('服务器错误: ${e.message} (状态码: ${e.statusCode})');
} on TimeoutError catch (e) {
  // 请求超时
  print('超时错误: ${e.message}');
} on ApiException catch (e) {
  // 其他 API 错误
  print('API 错误: ${e.message}');
}
```

## 项目结构

```
lib/
├── core/
│   ├── network/
│   │   ├── siliconflow_dio_client.dart    # SiliconFlow Dio 客户端
│   │   ├── api_exception.dart             # API 异常定义
│   │   └── dio_error_handler.dart         # 错误处理
│   └── services/
│       ├── service_locator.dart           # 依赖注入配置
│       ├── storage_service.dart           # 存储服务
│       └── storage_keys.dart              # 存储键定义
├── data/
│   ├── models/
│   │   └── chat_message.dart              # 聊天消息模型
│   ├── services/
│   │   └── siliconflow_api_service.dart   # SiliconFlow API 服务
│   └── repositories/
│       └── chat_repository.dart           # 聊天仓库
└── presentation/
    └── pages/
        └── chat_page.dart                 # 聊天界面示例
```

## 获取 API Key

1. 访问 [SiliconFlow 控制台](https://cloud.siliconflow.cn/account/ak)
2. 注册并登录账户
3. 创建 API Key
4. 在应用中配置 API Key

## 示例应用

项目中包含了一个完整的聊天界面示例 (`chat_page.dart`)，展示了：

- API Key 配置
- 模型选择
- 普通聊天和流式响应
- 聊天记录管理
- 错误处理

## 最佳实践

1. **API Key 安全**：不要在代码中硬编码 API Key，使用安全的存储方式
2. **错误处理**：总是处理可能的网络错误和 API 错误
3. **用户体验**：提供加载状态和错误提示
4. **性能优化**：使用流式响应提升用户体验
5. **数据持久化**：保存重要的聊天记录

## 相关文档

- [网络实现规范](docs/network_spec.md)
- [轻量存储规范](docs/lightweight_storage_spec.md)
- [设计规范](docs/design_spec.md)

---

该实现完全遵循项目的网络架构规范，提供了类型安全、可维护、可扩展的 SiliconFlow API 集成方案。`