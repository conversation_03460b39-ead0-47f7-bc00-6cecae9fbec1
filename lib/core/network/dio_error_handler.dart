import 'package:dio/dio.dart';
import 'api_exception.dart';

class DioErrorHandler {
  static ApiException handleError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.sendTimeout:
        return TimeoutError();
        
      case DioExceptionType.connectionError:
        return NetworkError();
        
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final data = error.response?.data;
        
        if (statusCode == 401) {
          return UnauthorizedError();
        }
        
        if (statusCode != null && statusCode >= 400 && statusCode < 500) {
          String message = '请求参数错误';
          if (data is Map && data['message'] != null) {
            message = data['message'].toString();
          }
          return ClientError(
            message: message,
            statusCode: statusCode,
            errorCode: data is Map && data['error_code'] != null 
                ? data['error_code'].toString() 
                : null,
          );
        }
        
        if (statusCode != null && statusCode >= 500) {
          return ServerError(statusCode: statusCode);
        }
        
        return UnknownError();
        
      case DioExceptionType.cancel:
        return ApiException(message: '请求已取消');
        
      default:
        return UnknownError(message: error.message ?? '发生未知错误');
    }
  }
}