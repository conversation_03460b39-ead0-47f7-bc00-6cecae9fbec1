class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? errorCode;

  ApiException({
    required this.message,
    this.statusCode,
    this.errorCode,
  });

  @override
  String toString() => 'ApiException: $message${statusCode != null ? ' (Status: $statusCode)' : ''}';
}

class NetworkError extends ApiException {
  NetworkError({super.message = '网络连接失败，请检查网络设置'});
}

class ServerError extends ApiException {
  ServerError({super.message = '服务器内部错误，请稍后重试', super.statusCode});
}

class ClientError extends ApiException {
  ClientError({super.message = '请求参数错误', super.statusCode, super.errorCode});
}

class TimeoutError extends ApiException {
  TimeoutError({super.message = '请求超时，请重试'});
}

class UnauthorizedError extends ApiException {
  UnauthorizedError({super.message = '认证失败，请重新登录'})
      : super(statusCode: 401);
}

class UnknownError extends ApiException {
  UnknownError({super.message = '发生未知错误'});
}