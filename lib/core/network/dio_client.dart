import 'package:dio/dio.dart';
import 'interceptors/auth_interceptor.dart';
import 'interceptors/logging_interceptor.dart';

class DioClient {
  final Dio _dio;

  DioClient() : _dio = Dio() {
    _dio.options = BaseOptions(
      baseUrl: 'https://api.yourapp.com/v1/',
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 15),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    // 只在 Debug 模式下添加日志拦截器
    assert(() {
      _dio.interceptors.add(LoggingInterceptor());
      return true;
    }());
    
    _dio.interceptors.add(AuthInterceptor());
  }

  Dio get instance => _dio;
}