import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (kDebugMode) {
      print('--- Request ---');
      print('URL: ${options.uri}');
      print('Method: ${options.method}');
      print('Headers: ${options.headers}');
      if (options.data != null) {
        print('Body: ${options.data}');
      }
      print('---------------');
    }
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      print('--- Response ---');
      print('URL: ${response.requestOptions.uri}');
      print('Status Code: ${response.statusCode}');
      print('Data: ${response.data}');
      print('----------------');
    }
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (kDebugMode) {
      print('--- Error ---');
      print('URL: ${err.requestOptions.uri}');
      print('Method: ${err.requestOptions.method}');
      print('Error: ${err.message}');
      if (err.response != null) {
        print('Status Code: ${err.response?.statusCode}');
        print('Response Data: ${err.response?.data}');
      }
      print('-------------');
    }
    handler.next(err);
  }
}