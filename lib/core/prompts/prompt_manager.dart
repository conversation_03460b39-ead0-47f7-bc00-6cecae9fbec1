import 'prompt_template.dart';
import 'prompt_templates.dart';
import 'generic_prompt_template.dart';
import '../services/storage_service.dart';
import '../services/storage_keys.dart';

/// 提示词管理器
///
/// 这个类负责管理所有的系统提示词模板，包括：
/// - 模板的注册和获取
/// - 用户自定义模板的管理
/// - 模板的持久化存储
/// - 模板使用统计
class PromptManager {
  static PromptManager? _instance;

  /// 已注册的模板集合
  final Map<String, PromptTemplate> _templates = {};

  /// 用户自定义模板集合
  final Map<String, PromptTemplate> _customTemplates = {};

  /// 私有构造函数
  PromptManager._() {
    _initializeTemplates();
  }

  /// 获取单例实例
  static PromptManager get instance {
    _instance ??= PromptManager._();
    return _instance!;
  }

  /// 初始化预定义模板
  void _initializeTemplates() {
    final predefinedTemplates = PromptTemplates.getAllTemplates();
    for (final template in predefinedTemplates) {
      _templates[template.id] = template;
    }
  }

  /// 获取所有可用模板
  List<PromptTemplate> getAllTemplates() {
    return [..._templates.values, ..._customTemplates.values];
  }

  /// 获取预定义模板
  List<PromptTemplate> getPredefinedTemplates() {
    return _templates.values.toList();
  }

  /// 获取用户自定义模板
  List<PromptTemplate> getCustomTemplates() {
    return _customTemplates.values.toList();
  }

  /// 根据ID获取模板
  PromptTemplate? getTemplateById(String id) {
    return _templates[id] ?? _customTemplates[id];
  }

  /// 注册新模板
  bool registerTemplate(PromptTemplate template) {
    if (template.id.isEmpty) {
      return false;
    }

    // 验证模板
    if (!template.validate()) {
      return false;
    }

    // 检查是否已存在
    if (_templates.containsKey(template.id) ||
        _customTemplates.containsKey(template.id)) {
      return false;
    }

    _customTemplates[template.id] = template;
    return true;
  }

  /// 更新现有模板
  bool updateTemplate(String id, PromptTemplate newTemplate) {
    if (id.isEmpty || newTemplate.id.isEmpty) {
      return false;
    }

    // 验证新模板
    if (!newTemplate.validate()) {
      return false;
    }

    // 只能更新自定义模板
    if (!_customTemplates.containsKey(id)) {
      return false;
    }

    // 移除旧模板
    _customTemplates.remove(id);

    // 添加新模板
    _customTemplates[newTemplate.id] = newTemplate;
    return true;
  }

  /// 删除模板
  bool removeTemplate(String id) {
    // 只能删除自定义模板
    if (!_customTemplates.containsKey(id)) {
      return false;
    }

    _customTemplates.remove(id);
    return true;
  }

  /// 搜索模板
  List<PromptTemplate> searchTemplates(String query) {
    if (query.trim().isEmpty) {
      return getAllTemplates();
    }

    final lowercaseQuery = query.toLowerCase();
    return getAllTemplates().where((template) {
      return template.name.toLowerCase().contains(lowercaseQuery) ||
          template.description.toLowerCase().contains(lowercaseQuery) ||
          template.id.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  /// 构建系统提示词
  String buildSystemPrompt({
    required String templateId,
    Map<String, dynamic>? context,
    Map<String, String>? variables,
  }) {
    final template = getTemplateById(templateId);
    if (template == null) {
      throw ArgumentError('未找到ID为 $templateId 的模板');
    }

    return template.buildPrompt(
      context: context,
      variables: variables,
    );
  }

  /// 验证模板变量
  bool validateTemplateVariables(
    String templateId,
    Map<String, String> variables,
  ) {
    final template = getTemplateById(templateId);
    if (template == null) {
      return false;
    }

    final requiredVars = template.getRequiredVariables();
    return requiredVars.every((varName) => variables.containsKey(varName));
  }

  /// 获取模板所需的变量
  List<String> getTemplateVariables(String templateId) {
    final template = getTemplateById(templateId);
    return template?.getRequiredVariables() ?? [];
  }

  /// 保存自定义模板到本地存储
  Future<void> saveCustomTemplates() async {
    try {
      final templatesData = _customTemplates.values.map((template) {
        return {
          'id': template.id,
          'name': template.name,
          'description': template.description,
          'version': template.version,
          'type': 'custom',
        };
      }).toList();

      await StorageService.setString(
        StorageKeys.customPromptTemplates,
        templatesData.toString(),
      );
    } catch (e) {
      // 保存失败不影响主流程
    }
  }

  /// 从本地存储加载自定义模板
  Future<void> loadCustomTemplates() async {
    try {
      final templatesJson =
          StorageService.getString(StorageKeys.customPromptTemplates);
      if (templatesJson.isEmpty) {
        return;
      }

      // 这里应该根据实际保存的模板数据进行反序列化
      // 由于模板可能很复杂，建议只保存模板的元数据
    } catch (e) {
      // 加载失败不影响主流程
    }
  }

  /// 清除所有自定义模板
  void clearCustomTemplates() {
    _customTemplates.clear();
  }

  /// 获取模板统计信息
  Map<String, dynamic> getStatistics() {
    return {
      'totalTemplates': getAllTemplates().length,
      'predefinedTemplates': _templates.length,
      'customTemplates': _customTemplates.length,
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// 批量验证模板
  Map<String, bool> batchValidateTemplates(List<String> templateIds) {
    final results = <String, bool>{};

    for (final id in templateIds) {
      final template = getTemplateById(id);
      results[id] = template?.validate() ?? false;
    }

    return results;
  }

  /// 复制模板
  PromptTemplate? duplicateTemplate(String sourceId, String newId) {
    final sourceTemplate = getTemplateById(sourceId);
    if (sourceTemplate == null) {
      return null;
    }

    // 只能复制自定义模板
    if (!_customTemplates.containsKey(sourceId)) {
      return null;
    }

    // 创建副本
    final copy = GenericPromptTemplate(
      id: newId,
      name: '${sourceTemplate.name} (副本)',
      description: sourceTemplate.description,
      version: sourceTemplate.version,
      templateContent: sourceTemplate.getTemplateContent(),
      requiredVariables: sourceTemplate.getRequiredVariables(),
    );

    _customTemplates[newId] = copy;
    return copy;
  }
}
