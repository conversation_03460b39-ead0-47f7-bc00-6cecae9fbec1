import 'prompt_template.dart';

/// 通用提示词模板实现
///
/// 这个类提供了一个灵活的提示词模板实现，支持动态内容替换
class GenericPromptTemplate extends PromptTemplate {
  /// 提示词模板内容
  final String _templateContent;

  /// 模板所需变量列表
  final List<String> _requiredVariables;

  const GenericPromptTemplate({
    required String id,
    required String name,
    required String description,
    required String version,
    required String templateContent,
    List<String> requiredVariables = const [],
  })  : _templateContent = templateContent,
        _requiredVariables = requiredVariables,
        super(
          id: id,
          name: name,
          description: description,
          version: version,
        );

  @override
  String buildPrompt({
    Map<String, dynamic>? context,
    Map<String, String>? variables,
  }) {
    // 1. 获取基础模板内容
    String content = getTemplateContent();

    // 2. 替换变量
    content = replaceVariables(content, variables);

    // 3. 添加上下文信息
    content = addContext(content, context);

    return content;
  }

  @override
  String getTemplateContent() {
    return _templateContent;
  }

  @override
  bool validate() {
    // 检查模板内容是否为空
    if (_templateContent.trim().isEmpty) {
      return false;
    }

    // 检查必需的变量是否在模板中存在
    for (final variable in _requiredVariables) {
      final placeholder = '{{$variable}}';
      if (!_templateContent.contains(placeholder)) {
        return false;
      }
    }

    return true;
  }

  @override
  List<String> getRequiredVariables() {
    return List.unmodifiable(_requiredVariables);
  }

  /// 创建模板副本并更新内容
  ///
  /// [newContent] 新的模板内容
  /// [newVariables] 新的变量列表
  GenericPromptTemplate copyWith({
    String? newContent,
    List<String>? newVariables,
  }) {
    return GenericPromptTemplate(
      id: id,
      name: name,
      description: description,
      version: version,
      templateContent: newContent ?? _templateContent,
      requiredVariables: newVariables ?? _requiredVariables,
    );
  }
}
