/// 系统提示词模板基类
///
/// 这个抽象类定义了所有系统提示词模板的基本结构和行为。
/// 具体的提示词模板应该继承这个类并实现其抽象方法。
abstract class PromptTemplate {
  /// 提示词模板的唯一标识符
  final String id;

  /// 提示词模板的名称
  final String name;

  /// 提示词模板的描述
  final String description;

  /// 提示词模板的版本
  final String version;

  const PromptTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.version,
  });

  /// 构建完整的系统提示词
  ///
  /// [context] 可选的上下文信息，用于个性化提示词
  /// [variables] 可变的参数映射，用于动态替换模板中的变量
  String buildPrompt({
    Map<String, dynamic>? context,
    Map<String, String>? variables,
  }) {
    // 1. 获取基础模板内容
    String content = getTemplateContent();

    // 2. 替换变量
    content = replaceVariables(content, variables);

    // 3. 添加上下文信息
    content = addContext(content, context);

    return content;
  }

  /// 获取提示词模板的基础内容
  ///
  /// 子类必须实现这个方法来提供具体的提示词内容
  String getTemplateContent();

  /// 验证提示词模板的完整性
  ///
  /// 检查模板是否包含所有必需的部分
  bool validate();

  /// 获取模板所需的变量列表
  ///
  /// 返回模板中定义的变量名称列表
  List<String> getRequiredVariables();

  /// 替换模板中的变量
  ///
  /// [template] 原始模板内容
  /// [variables] 变量映射
  String replaceVariables(String template, Map<String, String>? variables) {
    if (variables == null || variables.isEmpty) {
      return template;
    }

    String result = template;
    variables.forEach((key, value) {
      result = result.replaceAll('{{$key}}', value);
    });

    return result;
  }

  /// 添加上下文信息到提示词
  ///
  /// [basePrompt] 基础提示词
  /// [context] 上下文信息
  String addContext(String basePrompt, Map<String, dynamic>? context) {
    if (context == null || context.isEmpty) {
      return basePrompt;
    }

    final contextInfo = StringBuffer();

    if (context.containsKey('userRole')) {
      contextInfo.writeln('用户角色: ${context['userRole']}');
    }

    if (context.containsKey('taskType')) {
      contextInfo.writeln('任务类型: ${context['taskType']}');
    }

    if (context.containsKey('language')) {
      contextInfo.writeln('语言: ${context['language']}');
    }

    if (context.containsKey('additionalInfo')) {
      contextInfo.writeln('额外信息: ${context['additionalInfo']}');
    }

    if (contextInfo.isEmpty) {
      return basePrompt;
    }

    return '''
$basePrompt

上下文信息：
-----------
${contextInfo.toString()}
''';
  }

  @override
  String toString() {
    return 'PromptTemplate(id: $id, name: $name, version: $version)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is PromptTemplate &&
        other.id == id &&
        other.name == name &&
        other.version == version;
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode ^ version.hashCode;
}
