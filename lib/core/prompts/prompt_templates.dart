import 'prompt_template.dart';
import 'generic_prompt_template.dart';

/// 预定义的系统提示词模板集合
///
/// 这个类包含了常用的系统提示词模板，用于指导AI模型的行为
class PromptTemplates {
  /// 私有构造函数，防止实例化
  PromptTemplates._();

  /// 通用助手模板
//   static const PromptTemplate generalAssistant = GenericPromptTemplate(
//     id: 'general_assistant',
//     name: '通用助手',
//     description: '适用于各种通用对话任务的提示词模板',
//     version: '1.0.0',
//     templateContent: '''
// 你是一个友好的AI助手，能够帮助用户解决各种问题。
// 请用中文回答，除非用户明确要求使用其他语言。
// 你的回答应该：
// 1. 准确、有用且易于理解
// 2. 保持礼貌和专业的语气
// 3. 在适当的时候提供详细的解释
// 4. 如果不确定，请诚实地告知用户
// 5. 避免提供有害、危险或不道德的建议
//
// 请记住，你是一个AI助手，没有个人情感或意识。
// ''',
//   );

  /// 圆桌会议
  static const PromptTemplate roundTableMeeting = GenericPromptTemplate(
    id: 'round_table_meeting',
    name: '圆桌会议',
    description: '不同角色围绕特定主题进行深入讨论',
    version: '1.0.0',
    templateContent: '''
# 任务
为以下文章内容生成一场圆桌会议的对话。

# 对话要求
1.  **会议目标:** 深入探讨文章的核心内容，并从不同角度提出观点。
2.  **角色设定 (请具体描述):**
      * **主持人:** (姓名/身份/性别/国籍)，风格沉稳，负责引导议题和控制节奏。
      * **角色A:** (姓名/身份/性别/国籍)，立场是 [例如：理论扎实，偏向学术分析，强调长期影响]。
      * **角色B:** (姓名/身份/性别/国籍)，立场是 [例如：从市场和商业应用角度出发，关注成本与效益]。
      * **角色C:** (姓名/身份/性别/国籍)，立场是 [例如：谨慎务实，重点关注社会稳定与法规风险]。
      * **角色D:** (姓名/身份/性别/国籍)，立场是 [例如：从普通人视角出发，关心切身利益和伦理道德]。
3.  **核心讨论议题:**
      * 议题一：[指定文章中的第一个关键点，例如：该技术的最大机遇与挑战是什么？]
      * 议题二：[指定文章中的第二个关键点，例如：它将如何影响社会结构/普通人的生活？]
      * 议题三：[指定一个前瞻性问题，例如：我们应如何制定相应政策来引导其健康发展？]
4.  **对话氛围:** 专业、理性，但允许有观点的交锋。
5.  **输出格式:**
      * 以“主持人”的开场白开始，以“主持人”的总结性陈词结束。
      * **以完整的JSON对象格式输出。**
      * 对话内容需自然流畅，符合人物身份。
      * 总字数控制在 [例如：2000] 字。

# 输出示例
```
{
  "meeting_metadata": {
    "theme": "大模型'降智'现象与技术迭代的挑战",
  },
  "participants": [
    {
      "role_id": "主持人",
      "name": "Emma Scott",
      "identity": "科技媒体主编",
      "gender": "女",
      "nationality": "美国籍"
    },
    {
      "role_id": "角色A",
      "name": "Aidan McLaughlin",
      "identity": "OpenAI研究科学家",
      "perspective": "技术视角，认为存在'模型降智幻觉'",
      "gender": "男",
      "nationality": "美国墨西哥裔"
    },
    {
      "role_id": "角色D",
      "name": "Lena Zhou",
      "identity": "用户体验专家",
      "perspective": "关注用户反馈和信任危机",
      "gender": "女",
      "nationality": "中国籍"
    },
    {
      "role_id": "角色B",
      "name": "Mark Chen",
      "identity": "Anthropic产品负责人",
      "perspective": "官方立场，承认质量波动但否认故意削弱",
      "gender": "男",
      "nationality": "美国华裔"
    },
    {
      "role_id": "角色A_sub",
      "name": "James Lin",
      "identity": "开发者",
      "perspective": "技术实践者，验证实际体验",
      "gender": "男",
      "nationality": "美国韩裔"
    },
    {
      "role_id": "角色C",
      "name": "Sophia Park",
      "identity": "行业观察者",
      "perspective": "市场与趋势分析",
      "gender": "女",
      "nationality": "法国非裔"
    }
  ],
  "dialogue_flow": [
    {
      "turn": 1,
      "speaker": "Emma Scott(主持人)",
      "content": "大家好，今天我们讨论的话题是近期大模型领域频繁出现的'降智'争议。从OpenAI到Anthropic，用户对模型性能的质疑似乎愈演愈烈。首先，请Aidan分享一下您对'降智'现象的看法。"
    },
    {
      "turn": 2,
      "speaker": "Aidan McLaughlin",
      "content": "感谢主持人。其实，我观察到一个有趣的现象——很多用户（包括我自己）会错误地认为某个模型被实验室'削弱'了。比如，当OpenAI发布新版本时，用户可能因为某些功能体验不如预期，就下意识归因于模型能力下降。这种认知偏差可能源于对技术迭代的不理解，甚至是一种普遍的心理错觉。我建议我们称之为'模型降智幻觉'（Model Dumbing Illusion）。"
    },
    {
      "turn": 3,
      "speaker": "Mark Chen",
      "content": "确实，这是一次典型的'降智'事件。我们承认在推理堆栈更新后，Claude Opus 4.1在部分场景中出现了质量波动，比如回答格式错误或Code工具调用异常。但需要强调的是，我们的目标始终是保持模型质量，同时优化效率和吞吐量。这次更新属于技术调整，而非故意削弱模型能力。"
    },
    {
      "turn": 4,
      "speaker": "Lena Zhou",
      "content": "但用户反馈却显示问题持续存在，甚至有人称Claude Code'彻底废了'。这涉及到用户体验的深层问题。用户可能将技术调整的副作用（如响应延迟、功能限制）直接等同于模型能力下降，而忽视了系统优化的复杂性。比如，当Anthropic提到'凌晨两点用起来顺暢'，这暗示了资源分配或限流机制对用户体验的影响。用户对模型的期待往往建立在稳定性和一致性上，而技术迭代带来的波动容易引发信任危机。"
    },
    {
      "turn": 5,
      "speaker": "Emma Scott(主持人)",
      "content": "James，作为开发者，您是否观察到类似现象？"
    }
  ]
}
```

# 文章内容
[{{articleContent}}]
''',
  );

  /// 获取所有可用的模板
  static List<PromptTemplate> getAllTemplates() {
    return [
      // generalAssistant,
      roundTableMeeting,
    ];
  }

  /// 根据ID获取模板
  static PromptTemplate? getTemplateById(String id) {
    try {
      return getAllTemplates().firstWhere((template) => template.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 根据名称搜索模板
  static List<PromptTemplate> searchTemplates(String query) {
    final lowercaseQuery = query.toLowerCase();
    return getAllTemplates().where((template) {
      return template.name.toLowerCase().contains(lowercaseQuery) ||
          template.description.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }
}
