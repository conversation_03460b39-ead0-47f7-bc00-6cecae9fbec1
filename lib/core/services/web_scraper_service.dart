import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart' as dom;
import 'package:html2md/html2md.dart' as html2md;

/// 网页抓取结果模型
class WebScrapingResult {
  final String url;
  final String rawHtml;
  final String parsedHtml;
  final String markdown;
  final bool success;
  final String? error;

  const WebScrapingResult({
    required this.url,
    required this.rawHtml,
    required this.parsedHtml,
    required this.markdown,
    required this.success,
    this.error,
  });

  WebScrapingResult.error({
    required this.url,
    required this.error,
  })  : rawHtml = '',
        parsedHtml = '',
        markdown = '',
        success = false;
}

/// 网页抓取服务
class WebScraperService {
  static const Duration _defaultTimeout = Duration(seconds: 30);
  static const Map<String, String> _defaultHeaders = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
  };

  /// 抓取网页并转换为Markdown
  Future<WebScrapingResult> scrapeWebPage(String url) async {
    try {
      // 验证URL格式
      final uri = Uri.tryParse("https://r.jina.ai/$url");
      if (uri == null || (!uri.hasScheme || (!uri.scheme.startsWith('http')))) {
        return WebScrapingResult.error(
          url: url,
          error: '无效的URL格式',
        );
      }

      // 第一步：获取原始HTML内容
      final rawHtml = await _fetchRawHtml(uri);
      
      // 第二步：解析HTML结构
      final parsedHtml = _parseHtmlStructure(rawHtml);
      
      // 第三步：转换为Markdown
      final markdown = _convertToMarkdown(rawHtml);

      return WebScrapingResult(
        url: url,
        rawHtml: rawHtml,
        parsedHtml: parsedHtml,
        markdown: markdown,
        success: true,
      );
    } catch (e) {
      return WebScrapingResult.error(
        url: url,
        error: '抓取失败: ${e.toString()}',
      );
    }
  }

  /// 获取原始HTML内容
  Future<String> _fetchRawHtml(Uri uri) async {
    final response = await http.get(
      uri,
      headers: _defaultHeaders,
    ).timeout(_defaultTimeout);

    if (response.statusCode != 200) {
      throw Exception('HTTP错误: ${response.statusCode}');
    }

    // 处理编码
    String html = response.body;
    
    // 尝试从Content-Type头部获取编码
    String? charset = _extractCharsetFromContentType(response.headers['content-type']);
    
    // 如果没有从头部获取到编码，尝试从HTML meta标签获取
    charset ??= _extractCharsetFromHtml(html);
    
    // 如果指定了编码且不是UTF-8，尝试重新解码
    if (charset != null && charset.toLowerCase() != 'utf-8') {
      try {
        final bytes = response.bodyBytes;
        html = _decodeWithCharset(bytes, charset);
      } catch (e) {
        // 如果解码失败，使用原始内容
        html = response.body;
      }
    }

    return html;
  }

  /// 解析HTML结构并格式化显示
  String _parseHtmlStructure(String html) {
    final document = html_parser.parse(html);
    final buffer = StringBuffer();
    
    // 添加文档信息
    buffer.writeln('=== HTML文档结构 ===\n');
    
    // 获取标题
    final title = document.querySelector('title')?.text?.trim();
    if (title != null && title.isNotEmpty) {
      buffer.writeln('标题: $title\n');
    }
    
    // 获取meta信息
    final metaTags = document.querySelectorAll('meta');
    if (metaTags.isNotEmpty) {
      buffer.writeln('Meta信息:');
      for (final meta in metaTags.take(10)) { // 限制显示前10个meta标签
        final name = meta.attributes['name'] ?? meta.attributes['property'] ?? meta.attributes['http-equiv'];
        final content = meta.attributes['content'];
        if (name != null && content != null) {
          buffer.writeln('  $name: $content');
        }
      }
      buffer.writeln();
    }
    
    // 获取主要内容结构
    buffer.writeln('主要内容结构:');
    _parseElementStructure(document.body, buffer, 0);
    
    return buffer.toString();
  }

  /// 递归解析元素结构
  void _parseElementStructure(dom.Element? element, StringBuffer buffer, int depth) {
    if (element == null || depth > 3) return; // 限制深度避免过长
    
    final indent = '  ' * depth;
    final tagName = element.localName?.toUpperCase() ?? 'UNKNOWN';
    
    // 获取重要属性
    final id = element.attributes['id'];
    final className = element.attributes['class'];
    final href = element.attributes['href'];
    
    String tagInfo = '$indent<$tagName';
    if (id != null) tagInfo += ' id="$id"';
    if (className != null) tagInfo += ' class="$className"';
    if (href != null) tagInfo += ' href="$href"';
    tagInfo += '>';
    
    // 如果有文本内容，显示前50个字符
    final text = element.text.trim();
    if (text.isNotEmpty && element.children.isEmpty) {
      final shortText = text.length > 50 ? '${text.substring(0, 50)}...' : text;
      tagInfo += ' "$shortText"';
    }
    
    buffer.writeln(tagInfo);
    
    // 递归处理子元素（只处理重要的标签）
    final importantTags = {'div', 'section', 'article', 'header', 'footer', 'nav', 'main', 'aside', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol', 'li', 'a', 'img'};
    for (final child in element.children) {
      if (importantTags.contains(child.localName)) {
        _parseElementStructure(child, buffer, depth + 1);
      }
    }
  }

  /// 转换为Markdown格式
  String _convertToMarkdown(String html) {
    try {
      // 使用html2md库进行转换
      final markdown = html2md.convert(html, styleOptions: {
        'headingStyle': 'atx', // 使用 # 风格的标题
        'bulletListMarker': '-', // 使用 - 作为列表标记
        'codeBlockStyle': 'fenced', // 使用围栏代码块
        'emDelimiter': '*', // 使用 * 作为斜体标记
        'strongDelimiter': '**', // 使用 ** 作为粗体标记
      });
      
      // 清理和格式化Markdown
      return _cleanupMarkdown(markdown);
    } catch (e) {
      return '转换为Markdown时出错: ${e.toString()}';
    }
  }

  /// 清理和格式化Markdown内容
  String _cleanupMarkdown(String markdown) {
    // 移除多余的空行
    String cleaned = markdown.replaceAll(RegExp(r'\n\s*\n\s*\n'), '\n\n');
    
    // 移除行首行尾的空白字符
    cleaned = cleaned.split('\n').map((line) => line.trim()).join('\n');
    
    // 移除开头和结尾的空行
    cleaned = cleaned.trim();
    
    return cleaned;
  }

  /// 从Content-Type头部提取字符编码
  String? _extractCharsetFromContentType(String? contentType) {
    if (contentType == null) return null;
    
    final match = RegExp(r'charset=([^;]+)').firstMatch(contentType.toLowerCase());
    return match?.group(1)?.trim();
  }

  /// 从HTML meta标签提取字符编码
  String? _extractCharsetFromHtml(String html) {
    // 简化的字符编码检测
    final lowerHtml = html.toLowerCase();

    // 查找 charset= 模式
    final charsetIndex = lowerHtml.indexOf('charset=');
    if (charsetIndex != -1) {
      final start = charsetIndex + 8; // 'charset='.length
      var end = start;

      // 查找结束位置
      while (end < html.length) {
        final char = html[end];
        if (char == ';' || char == ' ' || char == '"' || char == "'" || char == '<' || char == '>') {
          break;
        }
        end++;
      }

      if (end > start) {
        return html.substring(start, end).trim();
      }
    }

    return null;
  }

  /// 使用指定编码解码字节数据
  String _decodeWithCharset(List<int> bytes, String charset) {
    // 这里简化处理，实际项目中可能需要更复杂的编码处理
    // 可以考虑使用 charset_converter 包来处理更多编码格式
    switch (charset.toLowerCase()) {
      case 'utf-8':
      case 'utf8':
        return utf8.decode(bytes);
      case 'gbk':
      case 'gb2312':
      case 'gb18030':
        // 对于中文编码，这里简化处理，实际可能需要专门的解码库
        return utf8.decode(bytes, allowMalformed: true);
      default:
        return utf8.decode(bytes, allowMalformed: true);
    }
  }
}
