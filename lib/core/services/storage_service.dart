import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  // 私有构造函数，防止该类被实例化
  StorageService._();

  static SharedPreferences? _prefs;

  // 静态的异步初始化方法
  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // --- 通用方法 (全部为静态) ---

  static Future<bool> setString(String key, String value) async {
    return await _prefs!.setString(key, value);
  }

  static String getString(String key, {String defaultValue = ''}) {
    return _prefs?.getString(key) ?? defaultValue;
  }

  static Future<bool> setBool(String key, bool value) async {
    return await _prefs!.setBool(key, value);
  }

  static bool getBool(String key, {bool defaultValue = false}) {
    return _prefs?.getBool(key) ?? defaultValue;
  }

  static Future<bool> setInt(String key, int value) async {
    return await _prefs!.setInt(key, value);
  }

  static int getInt(String key, {int defaultValue = 0}) {
    return _prefs?.getInt(key) ?? defaultValue;
  }

  // --- 清除数据 ---

  static Future<bool> remove(String key) async {
    return await _prefs!.remove(key);
  }

  static Future<bool> clear() async {
    return await _prefs!.clear();
  }
}
