import 'dart:io';
import 'package:syncfusion_flutter_pdf/pdf.dart';

/// 文档解析结果模型
class DocumentParsingResult {
  final String fileName;
  final String content;
  final String markdown;
  final bool success;
  final String? error;

  const DocumentParsingResult({
    required this.fileName,
    required this.content,
    required this.markdown,
    required this.success,
    this.error,
  });

  DocumentParsingResult.error({
    required this.fileName,
    required this.error,
  })  : content = '',
        markdown = '',
        success = false;
}

/// 文档解析服务
class DocumentParserService {
  /// 解析文档文件并转换为Markdown
  Future<DocumentParsingResult> parseDocument(String filePath) async {
    try {
      final file = File(filePath);
      final fileName = file.path.split('/').last;
      final extension = fileName.split('.').last.toLowerCase();

      if (!await file.exists()) {
        return DocumentParsingResult.error(
          fileName: fileName,
          error: '文件不存在',
        );
      }

      String content;
      switch (extension) {
        case 'pdf':
          content = await _parsePdf(file);
          break;
        case 'txt':
        case 'md':
          content = await _parseTextFile(file);
          break;
        case 'doc':
        case 'docx':
          // 对于Word文档，这里简化处理，实际项目中可能需要专门的库
          content = await _parseWordDocument(file);
          break;
        default:
          return DocumentParsingResult.error(
            fileName: fileName,
            error: '不支持的文件格式: $extension',
          );
      }

      final markdown = _convertToMarkdown(content, extension);

      return DocumentParsingResult(
        fileName: fileName,
        content: content,
        markdown: markdown,
        success: true,
      );
    } catch (e) {
      return DocumentParsingResult.error(
        fileName: filePath.split('/').last,
        error: '解析失败: ${e.toString()}',
      );
    }
  }

  /// 解析PDF文件
  Future<String> _parsePdf(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final document = PdfDocument(inputBytes: bytes);
      
      final StringBuffer textBuffer = StringBuffer();
      
      for (int i = 0; i < document.pages.count; i++) {
        final text = PdfTextExtractor(document).extractText(startPageIndex: i, endPageIndex: i);
        if (text.isNotEmpty) {
          textBuffer.writeln(text);
          textBuffer.writeln(); // 页面间添加空行
        }
      }
      
      document.dispose();
      return textBuffer.toString().trim();
    } catch (e) {
      throw Exception('PDF解析失败: ${e.toString()}');
    }
  }

  /// 解析文本文件
  Future<String> _parseTextFile(File file) async {
    try {
      return await file.readAsString();
    } catch (e) {
      throw Exception('文本文件读取失败: ${e.toString()}');
    }
  }

  /// 解析Word文档（简化实现）
  Future<String> _parseWordDocument(File file) async {
    // 这里是简化实现，实际项目中可能需要使用专门的Word解析库
    // 如 docx_to_text 或其他第三方库
    try {
      // 暂时返回提示信息，实际实现需要专门的Word解析库
      return '暂不支持Word文档解析，请转换为PDF或文本格式后重试。';
    } catch (e) {
      throw Exception('Word文档解析失败: ${e.toString()}');
    }
  }

  /// 将内容转换为Markdown格式
  String _convertToMarkdown(String content, String fileExtension) {
    switch (fileExtension) {
      case 'md':
        // Markdown文件直接返回
        return content;
      case 'txt':
      case 'pdf':
      case 'doc':
      case 'docx':
        // 对于其他格式，进行基本的Markdown格式化
        return _formatAsMarkdown(content);
      default:
        return content;
    }
  }

  /// 将纯文本格式化为Markdown
  String _formatAsMarkdown(String content) {
    final lines = content.split('\n');
    final buffer = StringBuffer();
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      
      if (line.isEmpty) {
        buffer.writeln();
        continue;
      }
      
      // 检测可能的标题（全大写或以数字开头的行）
      if (_isLikelyTitle(line)) {
        buffer.writeln('## $line');
      } else if (_isLikelySubtitle(line)) {
        buffer.writeln('### $line');
      } else {
        buffer.writeln(line);
      }
      
      buffer.writeln();
    }
    
    return buffer.toString().trim();
  }

  /// 检测是否可能是标题
  bool _isLikelyTitle(String line) {
    // 简单的标题检测逻辑
    if (line.length < 50 && line == line.toUpperCase() && line.contains(' ')) {
      return true;
    }
    
    // 检测以数字开头的标题
    final titlePattern = RegExp(r'^\d+[\.\s]');
    return titlePattern.hasMatch(line) && line.length < 100;
  }

  /// 检测是否可能是副标题
  bool _isLikelySubtitle(String line) {
    // 检测以字母和数字组合开头的副标题
    final subtitlePattern = RegExp(r'^[A-Za-z0-9]+[\.\)\s]');
    return subtitlePattern.hasMatch(line) && line.length < 80;
  }

  /// 验证文件类型是否支持
  bool isSupportedFileType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    return ['pdf', 'doc', 'docx', 'txt', 'md'].contains(extension);
  }

  /// 获取支持的文件扩展名列表
  List<String> getSupportedExtensions() {
    return ['pdf', 'doc', 'docx', 'txt', 'md'];
  }
}
