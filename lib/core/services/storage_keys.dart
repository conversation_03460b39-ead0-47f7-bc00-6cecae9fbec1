class StorageKeys {
  // 私有构造函数，防止该类被实例化
  StorageKeys._();

  // 用户认证 Token
  static const String userToken = 'user_token';

  // 应用主题模式 (例如：'light', 'dark', 'system')
  static const String appTheme = 'app_theme';

  // 是否首次打开应用
  static const String isFirstOpen = 'is_first_open';

  // 用户语言设置 (例如：'en', 'zh')
  static const String userLanguage = 'user_language';

  // SiliconFlow API Key
  static const String siliconFlowApiKey = 'siliconflow_api_key';

  // 聊天历史记录
  static const String chatHistory = 'chat_history';

  // 当前使用的模型
  static const String currentModel = 'current_model';

  // 系统提示词模板相关
  static const String customPromptTemplates = 'custom_prompt_templates';
  static const String lastUsedTemplate = 'last_used_template';
  static const String templateUsageStats = 'template_usage_stats';
}
