import '../../data/models/chat_message.dart';
import '../prompts/prompt_manager.dart';

/// 系统提示词集成服务
///
/// 这个服务展示了如何在聊天应用中集成系统提示词模板
class SystemPromptService {
  final PromptManager _promptManager = PromptManager.instance;

  /// 获取所有可用的提示词模板
  List<Map<String, dynamic>> getAvailableTemplates() {
    return _promptManager.getAllTemplates().map((template) {
      return {
        'id': template.id,
        'name': template.name,
        'description': template.description,
        'version': template.version,
        'requiredVariables': template.getRequiredVariables(),
      };
    }).toList();
  }

  /// 构建包含系统提示词的聊天消息列表
  List<ChatMessage> buildChatMessages({
    required String templateId,
    required List<ChatMessage> messages,
    Map<String, String>? variables,
    Map<String, dynamic>? context,
  }) {
    // 1. 构建系统提示词
    final systemPrompt = _promptManager.buildSystemPrompt(
      templateId: templateId,
      variables: variables,
      context: context,
    );

    // 3. 添加系统消息
    messages.insert(0, ChatMessage(
      role: 'system',
      content: systemPrompt,
    ));

    return messages;
  }

  /// 验证模板变量
  bool validateTemplateVariables({
    required String templateId,
    required Map<String, String> variables,
  }) {
    return _promptManager.validateTemplateVariables(templateId, variables);
  }

  /// 获取模板所需的变量
  List<String> getTemplateVariables(String templateId) {
    return _promptManager.getTemplateVariables(templateId);
  }

  /// 搜索模板
  List<Map<String, dynamic>> searchTemplates(String query) {
    return _promptManager.searchTemplates(query).map((template) {
      return {
        'id': template.id,
        'name': template.name,
        'description': template.description,
        'version': template.version,
        'requiredVariables': template.getRequiredVariables(),
      };
    }).toList();
  }

  /// 获取模板统计信息
  Map<String, dynamic> getTemplateStatistics() {
    return _promptManager.getStatistics();
  }
}
