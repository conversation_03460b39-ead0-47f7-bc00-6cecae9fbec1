import 'package:get_it/get_it.dart';
import 'package:round_table/core/services/system_prompt_service.dart';
import '../network/dio_client.dart';
import '../network/siliconflow_dio_client.dart';
import '../../data/services/siliconflow_api_service.dart';
import '../../data/services/tts_api_service.dart';
import '../../data/services/audio_player_manager.dart';
import '../../data/services/dialogue_tts_service.dart';
import '../../data/repositories/chat_repository.dart';

final getIt = GetIt.instance;

void setupServiceLocator() {
  // 注册通用 DioClient
  getIt.registerSingleton<DioClient>(DioClient());

  // 注册 SiliconFlow DioClient
  getIt.registerSingleton<SiliconFlowDioClient>(SiliconFlowDioClient());

  // 注册 SiliconFlow API 服务
  getIt.registerSingleton<SiliconFlowApiService>(
    SiliconFlowApiService(),
  );

  // 注册系统提示词服务
  getIt.registerSingleton<SystemPromptService>(SystemPromptService());

  // 注册 TTS API 服务
  getIt.registerSingleton<TTSApiService>(TTSApiService());

  // 注册音频播放管理器
  getIt.registerSingleton<AudioPlayerManager>(AudioPlayerManager());

  // 注册对话TTS服务
  getIt.registerSingleton<DialogueTTSService>(
    DialogueTTSService(
      ttsApiService: getIt<TTSApiService>(),
      audioPlayerManager: getIt<AudioPlayerManager>(),
    ),
  );

  // 注册 Chat Repository
  getIt.registerSingleton<ChatRepository>(
    ChatRepository(getIt<SiliconFlowApiService>(), getIt<SystemPromptService>()),
  );
}
