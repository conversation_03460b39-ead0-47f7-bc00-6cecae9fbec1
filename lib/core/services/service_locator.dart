import 'package:get_it/get_it.dart';
import 'package:round_table/core/services/system_prompt_service.dart';
import '../network/dio_client.dart';
import '../network/siliconflow_dio_client.dart';
import '../../data/services/siliconflow_api_service.dart';
import '../../data/repositories/chat_repository.dart';

final getIt = GetIt.instance;

void setupServiceLocator() {
  // 注册通用 DioClient
  getIt.registerSingleton<DioClient>(DioClient());

  // 注册 SiliconFlow DioClient
  getIt.registerSingleton<SiliconFlowDioClient>(SiliconFlowDioClient());

  // 注册 SiliconFlow API 服务
  getIt.registerSingleton<SiliconFlowApiService>(
    SiliconFlowApiService(),
  );

  // 注册系统提示词服务
  getIt.registerSingleton<SystemPromptService>(SystemPromptService());

  // 注册 Chat Repository
  getIt.registerSingleton<ChatRepository>(
    ChatRepository(getIt<SiliconFlowApiService>(), getIt<SystemPromptService>()),
  );
}
