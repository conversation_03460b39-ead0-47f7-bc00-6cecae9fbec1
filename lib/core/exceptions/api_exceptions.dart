/// API异常基类
abstract class ApiException implements Exception {
  final String message;
  final int? statusCode;

  const ApiException({
    required this.message,
    this.statusCode,
  });

  @override
  String toString() => 'ApiException: $message';
}

/// 网络错误
class NetworkError extends ApiException {
  const NetworkError({
    required super.message,
    super.statusCode,
  });

  @override
  String toString() => 'NetworkError: $message';
}

/// 服务器错误
class ServerError extends ApiException {
  const ServerError({
    required super.message,
    super.statusCode,
  });

  @override
  String toString() => 'ServerError: $message';
}

/// 未授权错误
class UnauthorizedError extends ApiException {
  const UnauthorizedError({
    required super.message,
    super.statusCode = 401,
  });

  @override
  String toString() => 'UnauthorizedError: $message';
}

/// 资源未找到错误
class NotFoundError extends ApiException {
  const NotFoundError({
    required super.message,
    super.statusCode = 404,
  });

  @override
  String toString() => 'NotFoundError: $message';
}

/// 请求频率限制错误
class RateLimitError extends ApiException {
  const RateLimitError({
    required super.message,
    super.statusCode = 429,
  });

  @override
  String toString() => 'RateLimitError: $message';
}

/// 验证错误
class ValidationError extends ApiException {
  const ValidationError({
    required super.message,
    super.statusCode = 400,
  });

  @override
  String toString() => 'ValidationError: $message';
}

/// 未知错误
class UnknownError extends ApiException {
  const UnknownError({
    required super.message,
    super.statusCode,
  });

  @override
  String toString() => 'UnknownError: $message';
}
