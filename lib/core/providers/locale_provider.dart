import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../services/storage_service.dart';
import '../services/storage_keys.dart';

class LocaleProvider with ChangeNotifier {
  Locale? _locale;

  Locale? get locale => _locale;

  // 初始化时从存储加载语言设置
  Future<void> initialize() async {
    final String savedLanguage = StorageService.getString(StorageKeys.userLanguage);
    if (savedLanguage.isNotEmpty) {
      _locale = _parseLocale(savedLanguage);
      notifyListeners();
    }
  }

  // 设置新的语言
  Future<void> setLocale(Locale newLocale) async {
    if (!AppLocalizations.supportedLocales.contains(newLocale)) {
      return;
    }

    _locale = newLocale;
    
    // 保存到存储
    await StorageService.setString(StorageKeys.userLanguage, newLocale.toLanguageTag());
    
    notifyListeners();
  }

  // 清除语言设置
  Future<void> clearLocale() async {
    _locale = null;
    await StorageService.remove(StorageKeys.userLanguage);
    notifyListeners();
  }

  // 解析语言代码字符串到Locale对象
  Locale _parseLocale(String languageTag) {
    try {
      final parts = languageTag.split('-');
      if (parts.length == 2) {
        return Locale(parts[0], parts[1]);
      } else {
        return Locale(languageTag);
      }
    } catch (e) {
      return const Locale('en');
    }
  }

  // 获取当前语言的显示名称
  String getCurrentLanguageName(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    if (_locale == null) return l10n.english;

    switch (_locale!.languageCode) {
      case 'en':
        return l10n.english;
      case 'zh':
        if (_locale!.countryCode == 'TW') {
          return l10n.chineseTraditional;
        }
        return l10n.chineseTraditional;
      case 'ar':
        return l10n.arabic;
      case 'hi':
        return l10n.hindi;
      default:
        return l10n.english;
    }
  }
}