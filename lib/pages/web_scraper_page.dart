import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../core/services/web_scraper_service.dart';

class WebScraperPage extends StatefulWidget {
  const WebScraperPage({super.key});

  @override
  State<WebScraperPage> createState() => _WebScraperPageState();
}

class _WebScraperPageState extends State<WebScraperPage> with TickerProviderStateMixin {
  final TextEditingController _urlController = TextEditingController();
  final WebScraperService _scraperService = WebScraperService();
  
  WebScrapingResult? _result;
  bool _isLoading = false;
  late TabController _tabController;

  // 预设的测试URL
  final List<String> _presetUrls = [
    'https://www.example.com',
    'https://httpbin.org/html',
    'https://www.baidu.com',
    'https://github.com',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _urlController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _scrapeWebPage() async {
    final url = _urlController.text.trim();
    if (url.isEmpty) {
      _showSnackBar('请输入URL', isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
      _result = null;
    });

    try {
      final result = await _scraperService.scrapeWebPage(url);
      setState(() {
        _result = result;
        _isLoading = false;
      });

      if (!result.success) {
        _showSnackBar(result.error ?? '抓取失败', isError: true);
      } else {
        _showSnackBar('抓取成功！');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showSnackBar('发生错误: ${e.toString()}', isError: true);
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _copyToClipboard(String text, String type) {
    Clipboard.setData(ClipboardData(text: text));
    _showSnackBar('$type 已复制到剪贴板');
  }

  void _clearResults() {
    setState(() {
      _result = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('网页抓取工具'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_result != null)
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: _clearResults,
              tooltip: '清除结果',
            ),
        ],
      ),
      body: Column(
        children: [
          // URL输入区域
          _buildUrlInputSection(),
          
          // 结果显示区域
          Expanded(
            child: _buildResultsSection(),
          ),
        ],
      ),
    );
  }

  Widget _buildUrlInputSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // URL输入框
          TextField(
            controller: _urlController,
            decoration: const InputDecoration(
              labelText: '请输入网页URL',
              hintText: 'https://example.com',
              prefixIcon: Icon(Icons.link),
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.url,
            textInputAction: TextInputAction.go,
            onSubmitted: (_) => _scrapeWebPage(),
          ),
          
          const SizedBox(height: 12),
          
          // 预设URL快捷按钮
          Wrap(
            spacing: 8,
            children: _presetUrls.map((url) => ActionChip(
              label: Text(
                url.replaceAll('https://', '').replaceAll('www.', ''),
                style: const TextStyle(fontSize: 12),
              ),
              onPressed: () {
                _urlController.text = url;
              },
            )).toList(),
          ),
          
          const SizedBox(height: 12),
          
          // 抓取按钮
          ElevatedButton.icon(
            onPressed: _isLoading ? null : _scrapeWebPage,
            icon: _isLoading 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.download),
            label: Text(_isLoading ? '抓取中...' : '开始抓取'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsSection() {
    if (_result == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.web,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '输入URL并点击"开始抓取"来获取网页内容',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (!_result!.success) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '抓取失败',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[700],
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _result!.error ?? '未知错误',
                style: const TextStyle(fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Tab栏
        Container(
          color: Theme.of(context).colorScheme.surface,
          child: TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: '原始HTML', icon: Icon(Icons.code)),
              Tab(text: 'HTML结构', icon: Icon(Icons.account_tree)),
              Tab(text: 'Markdown', icon: Icon(Icons.text_fields)),
            ],
          ),
        ),
        
        // Tab内容
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildContentTab(
                title: '原始HTTP响应内容',
                content: _result!.rawHtml,
                type: 'HTML源码',
              ),
              _buildContentTab(
                title: '解析后的HTML结构',
                content: _result!.parsedHtml,
                type: 'HTML结构',
              ),
              _buildContentTab(
                title: '转换后的Markdown文本',
                content: _result!.markdown,
                type: 'Markdown',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContentTab({
    required String title,
    required String content,
    required String type,
  }) {
    return Column(
      children: [
        // 标题栏
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
              ),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
              ),
              TextButton.icon(
                onPressed: () => _copyToClipboard(content, type),
                icon: const Icon(Icons.copy, size: 16),
                label: const Text('复制'),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                ),
              ),
            ],
          ),
        ),
        
        // 内容区域
        Expanded(
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            child: content.isEmpty
                ? const Center(
                    child: Text(
                      '暂无内容',
                      style: TextStyle(color: Colors.grey),
                    ),
                  )
                : SingleChildScrollView(
                    child: SelectableText(
                      content,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                        height: 1.4,
                      ),
                    ),
                  ),
          ),
        ),
      ],
    );
  }
}
