import 'package:freezed_annotation/freezed_annotation.dart';

part 'tts_models.freezed.dart';
part 'tts_models.g.dart';

/// TTS请求模型
@freezed
class TTSRequest with _$TTSRequest {
  const factory TTSRequest({
    required String model,
    required String input,
    String? voice,
    @Default('mp3') String responseFormat,
    @Default(32000) int sampleRate,
    @Default(true) bool stream,
    @Default(1.0) double speed,
    @Default(0.0) double gain,
  }) = _TTSRequest;

  factory TTSRequest.fromJson(Map<String, dynamic> json) =>
      _$TTSRequestFromJson(json);
}

/// 音色枚举
enum TTSVoice {
  @JsonValue('fnlp/MOSS-TTSD-v0.5:alex')
  alexMale('fnlp/MOSS-TTSD-v0.5:alex', '男'),
  @JsonValue('fnlp/MOSS-TTSD-v0.5:anna')
  anna<PERSON><PERSON>le('fnlp/MOSS-TTSD-v0.5:anna', '女'),
  @JsonValue('fnlp/MOSS-TTSD-v0.5:bella')
  bellaFemale('fnlp/MOSS-TTSD-v0.5:bella', '女'),
  @JsonValue('fnlp/MOSS-TTSD-v0.5:benjamin')
  benjaminMale('fnlp/MOSS-TTSD-v0.5:benjamin', '男'),
  @JsonValue('fnlp/MOSS-TTSD-v0.5:charles')
  charlesMale('fnlp/MOSS-TTSD-v0.5:charles', '男'),
  @JsonValue('fnlp/MOSS-TTSD-v0.5:claire')
  claireFemale('fnlp/MOSS-TTSD-v0.5:claire', '女'),
  @JsonValue('fnlp/MOSS-TTSD-v0.5:david')
  davidMale('fnlp/MOSS-TTSD-v0.5:david', '男'),
  @JsonValue('fnlp/MOSS-TTSD-v0.5:diana')
  dianaFemale('fnlp/MOSS-TTSD-v0.5:diana', '女');

  const TTSVoice(this.value, this.gender);

  final String value;
  final String gender;

  /// 根据性别获取合适的音色
  static TTSVoice getVoiceByGender(String gender) {
    if (gender == '女' || gender.toLowerCase() == 'female') {
      // 女性音色列表
      final femaleVoices = [annaFemale, bellaFemale, claireFemale, dianaFemale];
      return femaleVoices[DateTime.now().millisecondsSinceEpoch % femaleVoices.length];
    } else {
      // 男性音色列表
      final maleVoices = [alexMale, benjaminMale, charlesMale, davidMale];
      return maleVoices[DateTime.now().millisecondsSinceEpoch % maleVoices.length];
    }
  }

  /// 获取所有女性音色
  static List<TTSVoice> get femaleVoices => [annaFemale, bellaFemale, claireFemale, dianaFemale];

  /// 获取所有男性音色
  static List<TTSVoice> get maleVoices => [alexMale, benjaminMale, charlesMale, davidMale];
}

/// TTS模型枚举
enum TTSModel {
  @JsonValue('fnlp/MOSS-TTSD-v0.5')
  mossTTSD('fnlp/MOSS-TTSD-v0.5'),
  @JsonValue('FunAudioLLM/CosyVoice2-0.5B')
  cosyVoice('FunAudioLLM/CosyVoice2-0.5B');

  const TTSModel(this.value);

  final String value;
}

/// 音频格式枚举
enum AudioFormat {
  @JsonValue('mp3')
  mp3('mp3'),
  @JsonValue('wav')
  wav('wav'),
  @JsonValue('opus')
  opus('opus'),
  @JsonValue('pcm')
  pcm('pcm');

  const AudioFormat(this.value);

  final String value;
}

/// TTS响应结果
@freezed
class TTSResult with _$TTSResult {
  const factory TTSResult({
    required List<int> audioData,
    required String format,
    required int sampleRate,
    String? filePath,
  }) = _TTSResult;

  factory TTSResult.fromJson(Map<String, dynamic> json) =>
      _$TTSResultFromJson(json);
}

/// 对话项TTS状态
@freezed
class DialogueTTSStatus with _$DialogueTTSStatus {
  const factory DialogueTTSStatus({
    required int turn,
    required String speaker,
    required String content,
    required String gender,
    @Default(TTSState.pending) TTSState state,
    String? audioFilePath,
    String? error,
    TTSVoice? voice,
  }) = _DialogueTTSStatus;

  factory DialogueTTSStatus.fromJson(Map<String, dynamic> json) =>
      _$DialogueTTSStatusFromJson(json);
}

/// TTS状态枚举
enum TTSState {
  pending,    // 等待处理
  generating, // 正在生成
  ready,      // 已生成，准备播放
  playing,    // 正在播放
  completed,  // 播放完成
  error,      // 出现错误
}
