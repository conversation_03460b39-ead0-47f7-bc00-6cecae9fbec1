// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tts_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TTSRequest _$TTSRequestFromJson(Map<String, dynamic> json) {
  return _TTSRequest.fromJson(json);
}

/// @nodoc
mixin _$TTSRequest {
  String get model => throw _privateConstructorUsedError;
  String get input => throw _privateConstructorUsedError;
  String? get voice => throw _privateConstructorUsedError;
  String get responseFormat => throw _privateConstructorUsedError;
  int get sampleRate => throw _privateConstructorUsedError;
  bool get stream => throw _privateConstructorUsedError;
  double get speed => throw _privateConstructorUsedError;
  double get gain => throw _privateConstructorUsedError;

  /// Serializes this TTSRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TTSRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TTSRequestCopyWith<TTSRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TTSRequestCopyWith<$Res> {
  factory $TTSRequestCopyWith(
          TTSRequest value, $Res Function(TTSRequest) then) =
      _$TTSRequestCopyWithImpl<$Res, TTSRequest>;
  @useResult
  $Res call(
      {String model,
      String input,
      String? voice,
      String responseFormat,
      int sampleRate,
      bool stream,
      double speed,
      double gain});
}

/// @nodoc
class _$TTSRequestCopyWithImpl<$Res, $Val extends TTSRequest>
    implements $TTSRequestCopyWith<$Res> {
  _$TTSRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TTSRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? model = null,
    Object? input = null,
    Object? voice = freezed,
    Object? responseFormat = null,
    Object? sampleRate = null,
    Object? stream = null,
    Object? speed = null,
    Object? gain = null,
  }) {
    return _then(_value.copyWith(
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      input: null == input
          ? _value.input
          : input // ignore: cast_nullable_to_non_nullable
              as String,
      voice: freezed == voice
          ? _value.voice
          : voice // ignore: cast_nullable_to_non_nullable
              as String?,
      responseFormat: null == responseFormat
          ? _value.responseFormat
          : responseFormat // ignore: cast_nullable_to_non_nullable
              as String,
      sampleRate: null == sampleRate
          ? _value.sampleRate
          : sampleRate // ignore: cast_nullable_to_non_nullable
              as int,
      stream: null == stream
          ? _value.stream
          : stream // ignore: cast_nullable_to_non_nullable
              as bool,
      speed: null == speed
          ? _value.speed
          : speed // ignore: cast_nullable_to_non_nullable
              as double,
      gain: null == gain
          ? _value.gain
          : gain // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TTSRequestImplCopyWith<$Res>
    implements $TTSRequestCopyWith<$Res> {
  factory _$$TTSRequestImplCopyWith(
          _$TTSRequestImpl value, $Res Function(_$TTSRequestImpl) then) =
      __$$TTSRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String model,
      String input,
      String? voice,
      String responseFormat,
      int sampleRate,
      bool stream,
      double speed,
      double gain});
}

/// @nodoc
class __$$TTSRequestImplCopyWithImpl<$Res>
    extends _$TTSRequestCopyWithImpl<$Res, _$TTSRequestImpl>
    implements _$$TTSRequestImplCopyWith<$Res> {
  __$$TTSRequestImplCopyWithImpl(
      _$TTSRequestImpl _value, $Res Function(_$TTSRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of TTSRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? model = null,
    Object? input = null,
    Object? voice = freezed,
    Object? responseFormat = null,
    Object? sampleRate = null,
    Object? stream = null,
    Object? speed = null,
    Object? gain = null,
  }) {
    return _then(_$TTSRequestImpl(
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      input: null == input
          ? _value.input
          : input // ignore: cast_nullable_to_non_nullable
              as String,
      voice: freezed == voice
          ? _value.voice
          : voice // ignore: cast_nullable_to_non_nullable
              as String?,
      responseFormat: null == responseFormat
          ? _value.responseFormat
          : responseFormat // ignore: cast_nullable_to_non_nullable
              as String,
      sampleRate: null == sampleRate
          ? _value.sampleRate
          : sampleRate // ignore: cast_nullable_to_non_nullable
              as int,
      stream: null == stream
          ? _value.stream
          : stream // ignore: cast_nullable_to_non_nullable
              as bool,
      speed: null == speed
          ? _value.speed
          : speed // ignore: cast_nullable_to_non_nullable
              as double,
      gain: null == gain
          ? _value.gain
          : gain // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TTSRequestImpl implements _TTSRequest {
  const _$TTSRequestImpl(
      {required this.model,
      required this.input,
      this.voice,
      this.responseFormat = 'mp3',
      this.sampleRate = 32000,
      this.stream = true,
      this.speed = 1.0,
      this.gain = 0.0});

  factory _$TTSRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$TTSRequestImplFromJson(json);

  @override
  final String model;
  @override
  final String input;
  @override
  final String? voice;
  @override
  @JsonKey()
  final String responseFormat;
  @override
  @JsonKey()
  final int sampleRate;
  @override
  @JsonKey()
  final bool stream;
  @override
  @JsonKey()
  final double speed;
  @override
  @JsonKey()
  final double gain;

  @override
  String toString() {
    return 'TTSRequest(model: $model, input: $input, voice: $voice, responseFormat: $responseFormat, sampleRate: $sampleRate, stream: $stream, speed: $speed, gain: $gain)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TTSRequestImpl &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.input, input) || other.input == input) &&
            (identical(other.voice, voice) || other.voice == voice) &&
            (identical(other.responseFormat, responseFormat) ||
                other.responseFormat == responseFormat) &&
            (identical(other.sampleRate, sampleRate) ||
                other.sampleRate == sampleRate) &&
            (identical(other.stream, stream) || other.stream == stream) &&
            (identical(other.speed, speed) || other.speed == speed) &&
            (identical(other.gain, gain) || other.gain == gain));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, model, input, voice,
      responseFormat, sampleRate, stream, speed, gain);

  /// Create a copy of TTSRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TTSRequestImplCopyWith<_$TTSRequestImpl> get copyWith =>
      __$$TTSRequestImplCopyWithImpl<_$TTSRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TTSRequestImplToJson(
      this,
    );
  }
}

abstract class _TTSRequest implements TTSRequest {
  const factory _TTSRequest(
      {required final String model,
      required final String input,
      final String? voice,
      final String responseFormat,
      final int sampleRate,
      final bool stream,
      final double speed,
      final double gain}) = _$TTSRequestImpl;

  factory _TTSRequest.fromJson(Map<String, dynamic> json) =
      _$TTSRequestImpl.fromJson;

  @override
  String get model;
  @override
  String get input;
  @override
  String? get voice;
  @override
  String get responseFormat;
  @override
  int get sampleRate;
  @override
  bool get stream;
  @override
  double get speed;
  @override
  double get gain;

  /// Create a copy of TTSRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TTSRequestImplCopyWith<_$TTSRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TTSResult _$TTSResultFromJson(Map<String, dynamic> json) {
  return _TTSResult.fromJson(json);
}

/// @nodoc
mixin _$TTSResult {
  List<int> get audioData => throw _privateConstructorUsedError;
  String get format => throw _privateConstructorUsedError;
  int get sampleRate => throw _privateConstructorUsedError;
  String? get filePath => throw _privateConstructorUsedError;

  /// Serializes this TTSResult to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TTSResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TTSResultCopyWith<TTSResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TTSResultCopyWith<$Res> {
  factory $TTSResultCopyWith(TTSResult value, $Res Function(TTSResult) then) =
      _$TTSResultCopyWithImpl<$Res, TTSResult>;
  @useResult
  $Res call(
      {List<int> audioData, String format, int sampleRate, String? filePath});
}

/// @nodoc
class _$TTSResultCopyWithImpl<$Res, $Val extends TTSResult>
    implements $TTSResultCopyWith<$Res> {
  _$TTSResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TTSResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? audioData = null,
    Object? format = null,
    Object? sampleRate = null,
    Object? filePath = freezed,
  }) {
    return _then(_value.copyWith(
      audioData: null == audioData
          ? _value.audioData
          : audioData // ignore: cast_nullable_to_non_nullable
              as List<int>,
      format: null == format
          ? _value.format
          : format // ignore: cast_nullable_to_non_nullable
              as String,
      sampleRate: null == sampleRate
          ? _value.sampleRate
          : sampleRate // ignore: cast_nullable_to_non_nullable
              as int,
      filePath: freezed == filePath
          ? _value.filePath
          : filePath // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TTSResultImplCopyWith<$Res>
    implements $TTSResultCopyWith<$Res> {
  factory _$$TTSResultImplCopyWith(
          _$TTSResultImpl value, $Res Function(_$TTSResultImpl) then) =
      __$$TTSResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<int> audioData, String format, int sampleRate, String? filePath});
}

/// @nodoc
class __$$TTSResultImplCopyWithImpl<$Res>
    extends _$TTSResultCopyWithImpl<$Res, _$TTSResultImpl>
    implements _$$TTSResultImplCopyWith<$Res> {
  __$$TTSResultImplCopyWithImpl(
      _$TTSResultImpl _value, $Res Function(_$TTSResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of TTSResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? audioData = null,
    Object? format = null,
    Object? sampleRate = null,
    Object? filePath = freezed,
  }) {
    return _then(_$TTSResultImpl(
      audioData: null == audioData
          ? _value._audioData
          : audioData // ignore: cast_nullable_to_non_nullable
              as List<int>,
      format: null == format
          ? _value.format
          : format // ignore: cast_nullable_to_non_nullable
              as String,
      sampleRate: null == sampleRate
          ? _value.sampleRate
          : sampleRate // ignore: cast_nullable_to_non_nullable
              as int,
      filePath: freezed == filePath
          ? _value.filePath
          : filePath // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TTSResultImpl implements _TTSResult {
  const _$TTSResultImpl(
      {required final List<int> audioData,
      required this.format,
      required this.sampleRate,
      this.filePath})
      : _audioData = audioData;

  factory _$TTSResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$TTSResultImplFromJson(json);

  final List<int> _audioData;
  @override
  List<int> get audioData {
    if (_audioData is EqualUnmodifiableListView) return _audioData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_audioData);
  }

  @override
  final String format;
  @override
  final int sampleRate;
  @override
  final String? filePath;

  @override
  String toString() {
    return 'TTSResult(audioData: $audioData, format: $format, sampleRate: $sampleRate, filePath: $filePath)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TTSResultImpl &&
            const DeepCollectionEquality()
                .equals(other._audioData, _audioData) &&
            (identical(other.format, format) || other.format == format) &&
            (identical(other.sampleRate, sampleRate) ||
                other.sampleRate == sampleRate) &&
            (identical(other.filePath, filePath) ||
                other.filePath == filePath));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_audioData),
      format,
      sampleRate,
      filePath);

  /// Create a copy of TTSResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TTSResultImplCopyWith<_$TTSResultImpl> get copyWith =>
      __$$TTSResultImplCopyWithImpl<_$TTSResultImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TTSResultImplToJson(
      this,
    );
  }
}

abstract class _TTSResult implements TTSResult {
  const factory _TTSResult(
      {required final List<int> audioData,
      required final String format,
      required final int sampleRate,
      final String? filePath}) = _$TTSResultImpl;

  factory _TTSResult.fromJson(Map<String, dynamic> json) =
      _$TTSResultImpl.fromJson;

  @override
  List<int> get audioData;
  @override
  String get format;
  @override
  int get sampleRate;
  @override
  String? get filePath;

  /// Create a copy of TTSResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TTSResultImplCopyWith<_$TTSResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DialogueTTSStatus _$DialogueTTSStatusFromJson(Map<String, dynamic> json) {
  return _DialogueTTSStatus.fromJson(json);
}

/// @nodoc
mixin _$DialogueTTSStatus {
  int get turn => throw _privateConstructorUsedError;
  String get speaker => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  String get gender => throw _privateConstructorUsedError;
  TTSState get state => throw _privateConstructorUsedError;
  String? get audioFilePath => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  TTSVoice? get voice => throw _privateConstructorUsedError;

  /// Serializes this DialogueTTSStatus to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DialogueTTSStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DialogueTTSStatusCopyWith<DialogueTTSStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DialogueTTSStatusCopyWith<$Res> {
  factory $DialogueTTSStatusCopyWith(
          DialogueTTSStatus value, $Res Function(DialogueTTSStatus) then) =
      _$DialogueTTSStatusCopyWithImpl<$Res, DialogueTTSStatus>;
  @useResult
  $Res call(
      {int turn,
      String speaker,
      String content,
      String gender,
      TTSState state,
      String? audioFilePath,
      String? error,
      TTSVoice? voice});
}

/// @nodoc
class _$DialogueTTSStatusCopyWithImpl<$Res, $Val extends DialogueTTSStatus>
    implements $DialogueTTSStatusCopyWith<$Res> {
  _$DialogueTTSStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DialogueTTSStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? turn = null,
    Object? speaker = null,
    Object? content = null,
    Object? gender = null,
    Object? state = null,
    Object? audioFilePath = freezed,
    Object? error = freezed,
    Object? voice = freezed,
  }) {
    return _then(_value.copyWith(
      turn: null == turn
          ? _value.turn
          : turn // ignore: cast_nullable_to_non_nullable
              as int,
      speaker: null == speaker
          ? _value.speaker
          : speaker // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      gender: null == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String,
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as TTSState,
      audioFilePath: freezed == audioFilePath
          ? _value.audioFilePath
          : audioFilePath // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      voice: freezed == voice
          ? _value.voice
          : voice // ignore: cast_nullable_to_non_nullable
              as TTSVoice?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DialogueTTSStatusImplCopyWith<$Res>
    implements $DialogueTTSStatusCopyWith<$Res> {
  factory _$$DialogueTTSStatusImplCopyWith(_$DialogueTTSStatusImpl value,
          $Res Function(_$DialogueTTSStatusImpl) then) =
      __$$DialogueTTSStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int turn,
      String speaker,
      String content,
      String gender,
      TTSState state,
      String? audioFilePath,
      String? error,
      TTSVoice? voice});
}

/// @nodoc
class __$$DialogueTTSStatusImplCopyWithImpl<$Res>
    extends _$DialogueTTSStatusCopyWithImpl<$Res, _$DialogueTTSStatusImpl>
    implements _$$DialogueTTSStatusImplCopyWith<$Res> {
  __$$DialogueTTSStatusImplCopyWithImpl(_$DialogueTTSStatusImpl _value,
      $Res Function(_$DialogueTTSStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialogueTTSStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? turn = null,
    Object? speaker = null,
    Object? content = null,
    Object? gender = null,
    Object? state = null,
    Object? audioFilePath = freezed,
    Object? error = freezed,
    Object? voice = freezed,
  }) {
    return _then(_$DialogueTTSStatusImpl(
      turn: null == turn
          ? _value.turn
          : turn // ignore: cast_nullable_to_non_nullable
              as int,
      speaker: null == speaker
          ? _value.speaker
          : speaker // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      gender: null == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String,
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as TTSState,
      audioFilePath: freezed == audioFilePath
          ? _value.audioFilePath
          : audioFilePath // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      voice: freezed == voice
          ? _value.voice
          : voice // ignore: cast_nullable_to_non_nullable
              as TTSVoice?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DialogueTTSStatusImpl implements _DialogueTTSStatus {
  const _$DialogueTTSStatusImpl(
      {required this.turn,
      required this.speaker,
      required this.content,
      required this.gender,
      this.state = TTSState.pending,
      this.audioFilePath,
      this.error,
      this.voice});

  factory _$DialogueTTSStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$DialogueTTSStatusImplFromJson(json);

  @override
  final int turn;
  @override
  final String speaker;
  @override
  final String content;
  @override
  final String gender;
  @override
  @JsonKey()
  final TTSState state;
  @override
  final String? audioFilePath;
  @override
  final String? error;
  @override
  final TTSVoice? voice;

  @override
  String toString() {
    return 'DialogueTTSStatus(turn: $turn, speaker: $speaker, content: $content, gender: $gender, state: $state, audioFilePath: $audioFilePath, error: $error, voice: $voice)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DialogueTTSStatusImpl &&
            (identical(other.turn, turn) || other.turn == turn) &&
            (identical(other.speaker, speaker) || other.speaker == speaker) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.audioFilePath, audioFilePath) ||
                other.audioFilePath == audioFilePath) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.voice, voice) || other.voice == voice));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, turn, speaker, content, gender,
      state, audioFilePath, error, voice);

  /// Create a copy of DialogueTTSStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DialogueTTSStatusImplCopyWith<_$DialogueTTSStatusImpl> get copyWith =>
      __$$DialogueTTSStatusImplCopyWithImpl<_$DialogueTTSStatusImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DialogueTTSStatusImplToJson(
      this,
    );
  }
}

abstract class _DialogueTTSStatus implements DialogueTTSStatus {
  const factory _DialogueTTSStatus(
      {required final int turn,
      required final String speaker,
      required final String content,
      required final String gender,
      final TTSState state,
      final String? audioFilePath,
      final String? error,
      final TTSVoice? voice}) = _$DialogueTTSStatusImpl;

  factory _DialogueTTSStatus.fromJson(Map<String, dynamic> json) =
      _$DialogueTTSStatusImpl.fromJson;

  @override
  int get turn;
  @override
  String get speaker;
  @override
  String get content;
  @override
  String get gender;
  @override
  TTSState get state;
  @override
  String? get audioFilePath;
  @override
  String? get error;
  @override
  TTSVoice? get voice;

  /// Create a copy of DialogueTTSStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DialogueTTSStatusImplCopyWith<_$DialogueTTSStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
