// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_message.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ChatMessage _$ChatMessageFromJson(Map<String, dynamic> json) {
  return _ChatMessage.fromJson(json);
}

/// @nodoc
mixin _$ChatMessage {
  String get role => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;

  /// Serializes this ChatMessage to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChatMessage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChatMessageCopyWith<ChatMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatMessageCopyWith<$Res> {
  factory $ChatMessageCopyWith(
          ChatMessage value, $Res Function(ChatMessage) then) =
      _$ChatMessageCopyWithImpl<$Res, ChatMessage>;
  @useResult
  $Res call({String role, String content});
}

/// @nodoc
class _$ChatMessageCopyWithImpl<$Res, $Val extends ChatMessage>
    implements $ChatMessageCopyWith<$Res> {
  _$ChatMessageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChatMessage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? role = null,
    Object? content = null,
  }) {
    return _then(_value.copyWith(
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChatMessageImplCopyWith<$Res>
    implements $ChatMessageCopyWith<$Res> {
  factory _$$ChatMessageImplCopyWith(
          _$ChatMessageImpl value, $Res Function(_$ChatMessageImpl) then) =
      __$$ChatMessageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String role, String content});
}

/// @nodoc
class __$$ChatMessageImplCopyWithImpl<$Res>
    extends _$ChatMessageCopyWithImpl<$Res, _$ChatMessageImpl>
    implements _$$ChatMessageImplCopyWith<$Res> {
  __$$ChatMessageImplCopyWithImpl(
      _$ChatMessageImpl _value, $Res Function(_$ChatMessageImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChatMessage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? role = null,
    Object? content = null,
  }) {
    return _then(_$ChatMessageImpl(
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatMessageImpl implements _ChatMessage {
  const _$ChatMessageImpl({required this.role, required this.content});

  factory _$ChatMessageImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatMessageImplFromJson(json);

  @override
  final String role;
  @override
  final String content;

  @override
  String toString() {
    return 'ChatMessage(role: $role, content: $content)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatMessageImpl &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.content, content) || other.content == content));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, role, content);

  /// Create a copy of ChatMessage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatMessageImplCopyWith<_$ChatMessageImpl> get copyWith =>
      __$$ChatMessageImplCopyWithImpl<_$ChatMessageImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatMessageImplToJson(
      this,
    );
  }
}

abstract class _ChatMessage implements ChatMessage {
  const factory _ChatMessage(
      {required final String role,
      required final String content}) = _$ChatMessageImpl;

  factory _ChatMessage.fromJson(Map<String, dynamic> json) =
      _$ChatMessageImpl.fromJson;

  @override
  String get role;
  @override
  String get content;

  /// Create a copy of ChatMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChatMessageImplCopyWith<_$ChatMessageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ChatCompletionRequest _$ChatCompletionRequestFromJson(
    Map<String, dynamic> json) {
  return _ChatCompletionRequest.fromJson(json);
}

/// @nodoc
mixin _$ChatCompletionRequest {
  String get model => throw _privateConstructorUsedError;
  List<ChatMessage> get messages => throw _privateConstructorUsedError;
  bool get stream => throw _privateConstructorUsedError;
  int? get maxTokens => throw _privateConstructorUsedError;
  int? get thinkingBudget => throw _privateConstructorUsedError;
  Map<String, dynamic>? get responseFormat =>
      throw _privateConstructorUsedError;

  /// Serializes this ChatCompletionRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChatCompletionRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChatCompletionRequestCopyWith<ChatCompletionRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatCompletionRequestCopyWith<$Res> {
  factory $ChatCompletionRequestCopyWith(ChatCompletionRequest value,
          $Res Function(ChatCompletionRequest) then) =
      _$ChatCompletionRequestCopyWithImpl<$Res, ChatCompletionRequest>;
  @useResult
  $Res call(
      {String model,
      List<ChatMessage> messages,
      bool stream,
      int? maxTokens,
      int? thinkingBudget,
      Map<String, dynamic>? responseFormat});
}

/// @nodoc
class _$ChatCompletionRequestCopyWithImpl<$Res,
        $Val extends ChatCompletionRequest>
    implements $ChatCompletionRequestCopyWith<$Res> {
  _$ChatCompletionRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChatCompletionRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? model = null,
    Object? messages = null,
    Object? stream = null,
    Object? maxTokens = freezed,
    Object? thinkingBudget = freezed,
    Object? responseFormat = freezed,
  }) {
    return _then(_value.copyWith(
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      messages: null == messages
          ? _value.messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<ChatMessage>,
      stream: null == stream
          ? _value.stream
          : stream // ignore: cast_nullable_to_non_nullable
              as bool,
      maxTokens: freezed == maxTokens
          ? _value.maxTokens
          : maxTokens // ignore: cast_nullable_to_non_nullable
              as int?,
      thinkingBudget: freezed == thinkingBudget
          ? _value.thinkingBudget
          : thinkingBudget // ignore: cast_nullable_to_non_nullable
              as int?,
      responseFormat: freezed == responseFormat
          ? _value.responseFormat
          : responseFormat // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChatCompletionRequestImplCopyWith<$Res>
    implements $ChatCompletionRequestCopyWith<$Res> {
  factory _$$ChatCompletionRequestImplCopyWith(
          _$ChatCompletionRequestImpl value,
          $Res Function(_$ChatCompletionRequestImpl) then) =
      __$$ChatCompletionRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String model,
      List<ChatMessage> messages,
      bool stream,
      int? maxTokens,
      int? thinkingBudget,
      Map<String, dynamic>? responseFormat});
}

/// @nodoc
class __$$ChatCompletionRequestImplCopyWithImpl<$Res>
    extends _$ChatCompletionRequestCopyWithImpl<$Res,
        _$ChatCompletionRequestImpl>
    implements _$$ChatCompletionRequestImplCopyWith<$Res> {
  __$$ChatCompletionRequestImplCopyWithImpl(_$ChatCompletionRequestImpl _value,
      $Res Function(_$ChatCompletionRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChatCompletionRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? model = null,
    Object? messages = null,
    Object? stream = null,
    Object? maxTokens = freezed,
    Object? thinkingBudget = freezed,
    Object? responseFormat = freezed,
  }) {
    return _then(_$ChatCompletionRequestImpl(
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      messages: null == messages
          ? _value._messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<ChatMessage>,
      stream: null == stream
          ? _value.stream
          : stream // ignore: cast_nullable_to_non_nullable
              as bool,
      maxTokens: freezed == maxTokens
          ? _value.maxTokens
          : maxTokens // ignore: cast_nullable_to_non_nullable
              as int?,
      thinkingBudget: freezed == thinkingBudget
          ? _value.thinkingBudget
          : thinkingBudget // ignore: cast_nullable_to_non_nullable
              as int?,
      responseFormat: freezed == responseFormat
          ? _value._responseFormat
          : responseFormat // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatCompletionRequestImpl implements _ChatCompletionRequest {
  const _$ChatCompletionRequestImpl(
      {required this.model,
      required final List<ChatMessage> messages,
      this.stream = false,
      this.maxTokens,
      this.thinkingBudget,
      final Map<String, dynamic>? responseFormat})
      : _messages = messages,
        _responseFormat = responseFormat;

  factory _$ChatCompletionRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatCompletionRequestImplFromJson(json);

  @override
  final String model;
  final List<ChatMessage> _messages;
  @override
  List<ChatMessage> get messages {
    if (_messages is EqualUnmodifiableListView) return _messages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_messages);
  }

  @override
  @JsonKey()
  final bool stream;
  @override
  final int? maxTokens;
  @override
  final int? thinkingBudget;
  final Map<String, dynamic>? _responseFormat;
  @override
  Map<String, dynamic>? get responseFormat {
    final value = _responseFormat;
    if (value == null) return null;
    if (_responseFormat is EqualUnmodifiableMapView) return _responseFormat;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'ChatCompletionRequest(model: $model, messages: $messages, stream: $stream, maxTokens: $maxTokens, thinkingBudget: $thinkingBudget, responseFormat: $responseFormat)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatCompletionRequestImpl &&
            (identical(other.model, model) || other.model == model) &&
            const DeepCollectionEquality().equals(other._messages, _messages) &&
            (identical(other.stream, stream) || other.stream == stream) &&
            (identical(other.maxTokens, maxTokens) ||
                other.maxTokens == maxTokens) &&
            (identical(other.thinkingBudget, thinkingBudget) ||
                other.thinkingBudget == thinkingBudget) &&
            const DeepCollectionEquality()
                .equals(other._responseFormat, _responseFormat));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      model,
      const DeepCollectionEquality().hash(_messages),
      stream,
      maxTokens,
      thinkingBudget,
      const DeepCollectionEquality().hash(_responseFormat));

  /// Create a copy of ChatCompletionRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatCompletionRequestImplCopyWith<_$ChatCompletionRequestImpl>
      get copyWith => __$$ChatCompletionRequestImplCopyWithImpl<
          _$ChatCompletionRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatCompletionRequestImplToJson(
      this,
    );
  }
}

abstract class _ChatCompletionRequest implements ChatCompletionRequest {
  const factory _ChatCompletionRequest(
          {required final String model,
          required final List<ChatMessage> messages,
          final bool stream,
          final int? maxTokens,
          final int? thinkingBudget,
          final Map<String, dynamic>? responseFormat}) =
      _$ChatCompletionRequestImpl;

  factory _ChatCompletionRequest.fromJson(Map<String, dynamic> json) =
      _$ChatCompletionRequestImpl.fromJson;

  @override
  String get model;
  @override
  List<ChatMessage> get messages;
  @override
  bool get stream;
  @override
  int? get maxTokens;
  @override
  int? get thinkingBudget;
  @override
  Map<String, dynamic>? get responseFormat;

  /// Create a copy of ChatCompletionRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChatCompletionRequestImplCopyWith<_$ChatCompletionRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ChatCompletionResponse _$ChatCompletionResponseFromJson(
    Map<String, dynamic> json) {
  return _ChatCompletionResponse.fromJson(json);
}

/// @nodoc
mixin _$ChatCompletionResponse {
  String get id => throw _privateConstructorUsedError;
  String get object => throw _privateConstructorUsedError;
  int get created => throw _privateConstructorUsedError;
  String get model => throw _privateConstructorUsedError;
  List<ChatChoice> get choices => throw _privateConstructorUsedError;
  ChatUsage get usage => throw _privateConstructorUsedError;

  /// Serializes this ChatCompletionResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChatCompletionResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChatCompletionResponseCopyWith<ChatCompletionResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatCompletionResponseCopyWith<$Res> {
  factory $ChatCompletionResponseCopyWith(ChatCompletionResponse value,
          $Res Function(ChatCompletionResponse) then) =
      _$ChatCompletionResponseCopyWithImpl<$Res, ChatCompletionResponse>;
  @useResult
  $Res call(
      {String id,
      String object,
      int created,
      String model,
      List<ChatChoice> choices,
      ChatUsage usage});

  $ChatUsageCopyWith<$Res> get usage;
}

/// @nodoc
class _$ChatCompletionResponseCopyWithImpl<$Res,
        $Val extends ChatCompletionResponse>
    implements $ChatCompletionResponseCopyWith<$Res> {
  _$ChatCompletionResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChatCompletionResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? object = null,
    Object? created = null,
    Object? model = null,
    Object? choices = null,
    Object? usage = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      object: null == object
          ? _value.object
          : object // ignore: cast_nullable_to_non_nullable
              as String,
      created: null == created
          ? _value.created
          : created // ignore: cast_nullable_to_non_nullable
              as int,
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      choices: null == choices
          ? _value.choices
          : choices // ignore: cast_nullable_to_non_nullable
              as List<ChatChoice>,
      usage: null == usage
          ? _value.usage
          : usage // ignore: cast_nullable_to_non_nullable
              as ChatUsage,
    ) as $Val);
  }

  /// Create a copy of ChatCompletionResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ChatUsageCopyWith<$Res> get usage {
    return $ChatUsageCopyWith<$Res>(_value.usage, (value) {
      return _then(_value.copyWith(usage: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ChatCompletionResponseImplCopyWith<$Res>
    implements $ChatCompletionResponseCopyWith<$Res> {
  factory _$$ChatCompletionResponseImplCopyWith(
          _$ChatCompletionResponseImpl value,
          $Res Function(_$ChatCompletionResponseImpl) then) =
      __$$ChatCompletionResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String object,
      int created,
      String model,
      List<ChatChoice> choices,
      ChatUsage usage});

  @override
  $ChatUsageCopyWith<$Res> get usage;
}

/// @nodoc
class __$$ChatCompletionResponseImplCopyWithImpl<$Res>
    extends _$ChatCompletionResponseCopyWithImpl<$Res,
        _$ChatCompletionResponseImpl>
    implements _$$ChatCompletionResponseImplCopyWith<$Res> {
  __$$ChatCompletionResponseImplCopyWithImpl(
      _$ChatCompletionResponseImpl _value,
      $Res Function(_$ChatCompletionResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChatCompletionResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? object = null,
    Object? created = null,
    Object? model = null,
    Object? choices = null,
    Object? usage = null,
  }) {
    return _then(_$ChatCompletionResponseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      object: null == object
          ? _value.object
          : object // ignore: cast_nullable_to_non_nullable
              as String,
      created: null == created
          ? _value.created
          : created // ignore: cast_nullable_to_non_nullable
              as int,
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      choices: null == choices
          ? _value._choices
          : choices // ignore: cast_nullable_to_non_nullable
              as List<ChatChoice>,
      usage: null == usage
          ? _value.usage
          : usage // ignore: cast_nullable_to_non_nullable
              as ChatUsage,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatCompletionResponseImpl implements _ChatCompletionResponse {
  const _$ChatCompletionResponseImpl(
      {required this.id,
      required this.object,
      required this.created,
      required this.model,
      required final List<ChatChoice> choices,
      required this.usage})
      : _choices = choices;

  factory _$ChatCompletionResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatCompletionResponseImplFromJson(json);

  @override
  final String id;
  @override
  final String object;
  @override
  final int created;
  @override
  final String model;
  final List<ChatChoice> _choices;
  @override
  List<ChatChoice> get choices {
    if (_choices is EqualUnmodifiableListView) return _choices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_choices);
  }

  @override
  final ChatUsage usage;

  @override
  String toString() {
    return 'ChatCompletionResponse(id: $id, object: $object, created: $created, model: $model, choices: $choices, usage: $usage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatCompletionResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.object, object) || other.object == object) &&
            (identical(other.created, created) || other.created == created) &&
            (identical(other.model, model) || other.model == model) &&
            const DeepCollectionEquality().equals(other._choices, _choices) &&
            (identical(other.usage, usage) || other.usage == usage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, object, created, model,
      const DeepCollectionEquality().hash(_choices), usage);

  /// Create a copy of ChatCompletionResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatCompletionResponseImplCopyWith<_$ChatCompletionResponseImpl>
      get copyWith => __$$ChatCompletionResponseImplCopyWithImpl<
          _$ChatCompletionResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatCompletionResponseImplToJson(
      this,
    );
  }
}

abstract class _ChatCompletionResponse implements ChatCompletionResponse {
  const factory _ChatCompletionResponse(
      {required final String id,
      required final String object,
      required final int created,
      required final String model,
      required final List<ChatChoice> choices,
      required final ChatUsage usage}) = _$ChatCompletionResponseImpl;

  factory _ChatCompletionResponse.fromJson(Map<String, dynamic> json) =
      _$ChatCompletionResponseImpl.fromJson;

  @override
  String get id;
  @override
  String get object;
  @override
  int get created;
  @override
  String get model;
  @override
  List<ChatChoice> get choices;
  @override
  ChatUsage get usage;

  /// Create a copy of ChatCompletionResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChatCompletionResponseImplCopyWith<_$ChatCompletionResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ChatChoice _$ChatChoiceFromJson(Map<String, dynamic> json) {
  return _ChatChoice.fromJson(json);
}

/// @nodoc
mixin _$ChatChoice {
  int get index => throw _privateConstructorUsedError;
  ChatMessage get message => throw _privateConstructorUsedError;
  String? get finishReason => throw _privateConstructorUsedError;

  /// Serializes this ChatChoice to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChatChoice
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChatChoiceCopyWith<ChatChoice> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatChoiceCopyWith<$Res> {
  factory $ChatChoiceCopyWith(
          ChatChoice value, $Res Function(ChatChoice) then) =
      _$ChatChoiceCopyWithImpl<$Res, ChatChoice>;
  @useResult
  $Res call({int index, ChatMessage message, String? finishReason});

  $ChatMessageCopyWith<$Res> get message;
}

/// @nodoc
class _$ChatChoiceCopyWithImpl<$Res, $Val extends ChatChoice>
    implements $ChatChoiceCopyWith<$Res> {
  _$ChatChoiceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChatChoice
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = null,
    Object? message = null,
    Object? finishReason = freezed,
  }) {
    return _then(_value.copyWith(
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as ChatMessage,
      finishReason: freezed == finishReason
          ? _value.finishReason
          : finishReason // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of ChatChoice
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ChatMessageCopyWith<$Res> get message {
    return $ChatMessageCopyWith<$Res>(_value.message, (value) {
      return _then(_value.copyWith(message: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ChatChoiceImplCopyWith<$Res>
    implements $ChatChoiceCopyWith<$Res> {
  factory _$$ChatChoiceImplCopyWith(
          _$ChatChoiceImpl value, $Res Function(_$ChatChoiceImpl) then) =
      __$$ChatChoiceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int index, ChatMessage message, String? finishReason});

  @override
  $ChatMessageCopyWith<$Res> get message;
}

/// @nodoc
class __$$ChatChoiceImplCopyWithImpl<$Res>
    extends _$ChatChoiceCopyWithImpl<$Res, _$ChatChoiceImpl>
    implements _$$ChatChoiceImplCopyWith<$Res> {
  __$$ChatChoiceImplCopyWithImpl(
      _$ChatChoiceImpl _value, $Res Function(_$ChatChoiceImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChatChoice
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = null,
    Object? message = null,
    Object? finishReason = freezed,
  }) {
    return _then(_$ChatChoiceImpl(
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as ChatMessage,
      finishReason: freezed == finishReason
          ? _value.finishReason
          : finishReason // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatChoiceImpl implements _ChatChoice {
  const _$ChatChoiceImpl(
      {required this.index, required this.message, this.finishReason});

  factory _$ChatChoiceImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatChoiceImplFromJson(json);

  @override
  final int index;
  @override
  final ChatMessage message;
  @override
  final String? finishReason;

  @override
  String toString() {
    return 'ChatChoice(index: $index, message: $message, finishReason: $finishReason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatChoiceImpl &&
            (identical(other.index, index) || other.index == index) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.finishReason, finishReason) ||
                other.finishReason == finishReason));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, index, message, finishReason);

  /// Create a copy of ChatChoice
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatChoiceImplCopyWith<_$ChatChoiceImpl> get copyWith =>
      __$$ChatChoiceImplCopyWithImpl<_$ChatChoiceImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatChoiceImplToJson(
      this,
    );
  }
}

abstract class _ChatChoice implements ChatChoice {
  const factory _ChatChoice(
      {required final int index,
      required final ChatMessage message,
      final String? finishReason}) = _$ChatChoiceImpl;

  factory _ChatChoice.fromJson(Map<String, dynamic> json) =
      _$ChatChoiceImpl.fromJson;

  @override
  int get index;
  @override
  ChatMessage get message;
  @override
  String? get finishReason;

  /// Create a copy of ChatChoice
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChatChoiceImplCopyWith<_$ChatChoiceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ChatUsage _$ChatUsageFromJson(Map<String, dynamic> json) {
  return _ChatUsage.fromJson(json);
}

/// @nodoc
mixin _$ChatUsage {
  int get promptTokens => throw _privateConstructorUsedError;
  int get completionTokens => throw _privateConstructorUsedError;
  int get totalTokens => throw _privateConstructorUsedError;

  /// Serializes this ChatUsage to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChatUsage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChatUsageCopyWith<ChatUsage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatUsageCopyWith<$Res> {
  factory $ChatUsageCopyWith(ChatUsage value, $Res Function(ChatUsage) then) =
      _$ChatUsageCopyWithImpl<$Res, ChatUsage>;
  @useResult
  $Res call({int promptTokens, int completionTokens, int totalTokens});
}

/// @nodoc
class _$ChatUsageCopyWithImpl<$Res, $Val extends ChatUsage>
    implements $ChatUsageCopyWith<$Res> {
  _$ChatUsageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChatUsage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? promptTokens = null,
    Object? completionTokens = null,
    Object? totalTokens = null,
  }) {
    return _then(_value.copyWith(
      promptTokens: null == promptTokens
          ? _value.promptTokens
          : promptTokens // ignore: cast_nullable_to_non_nullable
              as int,
      completionTokens: null == completionTokens
          ? _value.completionTokens
          : completionTokens // ignore: cast_nullable_to_non_nullable
              as int,
      totalTokens: null == totalTokens
          ? _value.totalTokens
          : totalTokens // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChatUsageImplCopyWith<$Res>
    implements $ChatUsageCopyWith<$Res> {
  factory _$$ChatUsageImplCopyWith(
          _$ChatUsageImpl value, $Res Function(_$ChatUsageImpl) then) =
      __$$ChatUsageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int promptTokens, int completionTokens, int totalTokens});
}

/// @nodoc
class __$$ChatUsageImplCopyWithImpl<$Res>
    extends _$ChatUsageCopyWithImpl<$Res, _$ChatUsageImpl>
    implements _$$ChatUsageImplCopyWith<$Res> {
  __$$ChatUsageImplCopyWithImpl(
      _$ChatUsageImpl _value, $Res Function(_$ChatUsageImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChatUsage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? promptTokens = null,
    Object? completionTokens = null,
    Object? totalTokens = null,
  }) {
    return _then(_$ChatUsageImpl(
      promptTokens: null == promptTokens
          ? _value.promptTokens
          : promptTokens // ignore: cast_nullable_to_non_nullable
              as int,
      completionTokens: null == completionTokens
          ? _value.completionTokens
          : completionTokens // ignore: cast_nullable_to_non_nullable
              as int,
      totalTokens: null == totalTokens
          ? _value.totalTokens
          : totalTokens // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatUsageImpl implements _ChatUsage {
  const _$ChatUsageImpl(
      {required this.promptTokens,
      required this.completionTokens,
      required this.totalTokens});

  factory _$ChatUsageImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatUsageImplFromJson(json);

  @override
  final int promptTokens;
  @override
  final int completionTokens;
  @override
  final int totalTokens;

  @override
  String toString() {
    return 'ChatUsage(promptTokens: $promptTokens, completionTokens: $completionTokens, totalTokens: $totalTokens)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatUsageImpl &&
            (identical(other.promptTokens, promptTokens) ||
                other.promptTokens == promptTokens) &&
            (identical(other.completionTokens, completionTokens) ||
                other.completionTokens == completionTokens) &&
            (identical(other.totalTokens, totalTokens) ||
                other.totalTokens == totalTokens));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, promptTokens, completionTokens, totalTokens);

  /// Create a copy of ChatUsage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatUsageImplCopyWith<_$ChatUsageImpl> get copyWith =>
      __$$ChatUsageImplCopyWithImpl<_$ChatUsageImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatUsageImplToJson(
      this,
    );
  }
}

abstract class _ChatUsage implements ChatUsage {
  const factory _ChatUsage(
      {required final int promptTokens,
      required final int completionTokens,
      required final int totalTokens}) = _$ChatUsageImpl;

  factory _ChatUsage.fromJson(Map<String, dynamic> json) =
      _$ChatUsageImpl.fromJson;

  @override
  int get promptTokens;
  @override
  int get completionTokens;
  @override
  int get totalTokens;

  /// Create a copy of ChatUsage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChatUsageImplCopyWith<_$ChatUsageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StreamingChatChoice _$StreamingChatChoiceFromJson(Map<String, dynamic> json) {
  return _StreamingChatChoice.fromJson(json);
}

/// @nodoc
mixin _$StreamingChatChoice {
  int get index => throw _privateConstructorUsedError;
  StreamingDelta get delta => throw _privateConstructorUsedError;
  String? get finishReason => throw _privateConstructorUsedError;

  /// Serializes this StreamingChatChoice to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StreamingChatChoice
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StreamingChatChoiceCopyWith<StreamingChatChoice> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StreamingChatChoiceCopyWith<$Res> {
  factory $StreamingChatChoiceCopyWith(
          StreamingChatChoice value, $Res Function(StreamingChatChoice) then) =
      _$StreamingChatChoiceCopyWithImpl<$Res, StreamingChatChoice>;
  @useResult
  $Res call({int index, StreamingDelta delta, String? finishReason});

  $StreamingDeltaCopyWith<$Res> get delta;
}

/// @nodoc
class _$StreamingChatChoiceCopyWithImpl<$Res, $Val extends StreamingChatChoice>
    implements $StreamingChatChoiceCopyWith<$Res> {
  _$StreamingChatChoiceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StreamingChatChoice
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = null,
    Object? delta = null,
    Object? finishReason = freezed,
  }) {
    return _then(_value.copyWith(
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      delta: null == delta
          ? _value.delta
          : delta // ignore: cast_nullable_to_non_nullable
              as StreamingDelta,
      finishReason: freezed == finishReason
          ? _value.finishReason
          : finishReason // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of StreamingChatChoice
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $StreamingDeltaCopyWith<$Res> get delta {
    return $StreamingDeltaCopyWith<$Res>(_value.delta, (value) {
      return _then(_value.copyWith(delta: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$StreamingChatChoiceImplCopyWith<$Res>
    implements $StreamingChatChoiceCopyWith<$Res> {
  factory _$$StreamingChatChoiceImplCopyWith(_$StreamingChatChoiceImpl value,
          $Res Function(_$StreamingChatChoiceImpl) then) =
      __$$StreamingChatChoiceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int index, StreamingDelta delta, String? finishReason});

  @override
  $StreamingDeltaCopyWith<$Res> get delta;
}

/// @nodoc
class __$$StreamingChatChoiceImplCopyWithImpl<$Res>
    extends _$StreamingChatChoiceCopyWithImpl<$Res, _$StreamingChatChoiceImpl>
    implements _$$StreamingChatChoiceImplCopyWith<$Res> {
  __$$StreamingChatChoiceImplCopyWithImpl(_$StreamingChatChoiceImpl _value,
      $Res Function(_$StreamingChatChoiceImpl) _then)
      : super(_value, _then);

  /// Create a copy of StreamingChatChoice
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = null,
    Object? delta = null,
    Object? finishReason = freezed,
  }) {
    return _then(_$StreamingChatChoiceImpl(
      index: null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      delta: null == delta
          ? _value.delta
          : delta // ignore: cast_nullable_to_non_nullable
              as StreamingDelta,
      finishReason: freezed == finishReason
          ? _value.finishReason
          : finishReason // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StreamingChatChoiceImpl implements _StreamingChatChoice {
  const _$StreamingChatChoiceImpl(
      {required this.index, required this.delta, this.finishReason});

  factory _$StreamingChatChoiceImpl.fromJson(Map<String, dynamic> json) =>
      _$$StreamingChatChoiceImplFromJson(json);

  @override
  final int index;
  @override
  final StreamingDelta delta;
  @override
  final String? finishReason;

  @override
  String toString() {
    return 'StreamingChatChoice(index: $index, delta: $delta, finishReason: $finishReason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StreamingChatChoiceImpl &&
            (identical(other.index, index) || other.index == index) &&
            (identical(other.delta, delta) || other.delta == delta) &&
            (identical(other.finishReason, finishReason) ||
                other.finishReason == finishReason));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, index, delta, finishReason);

  /// Create a copy of StreamingChatChoice
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StreamingChatChoiceImplCopyWith<_$StreamingChatChoiceImpl> get copyWith =>
      __$$StreamingChatChoiceImplCopyWithImpl<_$StreamingChatChoiceImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StreamingChatChoiceImplToJson(
      this,
    );
  }
}

abstract class _StreamingChatChoice implements StreamingChatChoice {
  const factory _StreamingChatChoice(
      {required final int index,
      required final StreamingDelta delta,
      final String? finishReason}) = _$StreamingChatChoiceImpl;

  factory _StreamingChatChoice.fromJson(Map<String, dynamic> json) =
      _$StreamingChatChoiceImpl.fromJson;

  @override
  int get index;
  @override
  StreamingDelta get delta;
  @override
  String? get finishReason;

  /// Create a copy of StreamingChatChoice
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StreamingChatChoiceImplCopyWith<_$StreamingChatChoiceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StreamingDelta _$StreamingDeltaFromJson(Map<String, dynamic> json) {
  return _StreamingDelta.fromJson(json);
}

/// @nodoc
mixin _$StreamingDelta {
  String? get role => throw _privateConstructorUsedError;
  String? get content => throw _privateConstructorUsedError;
  String? get reasoningContent => throw _privateConstructorUsedError;

  /// Serializes this StreamingDelta to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StreamingDelta
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StreamingDeltaCopyWith<StreamingDelta> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StreamingDeltaCopyWith<$Res> {
  factory $StreamingDeltaCopyWith(
          StreamingDelta value, $Res Function(StreamingDelta) then) =
      _$StreamingDeltaCopyWithImpl<$Res, StreamingDelta>;
  @useResult
  $Res call({String? role, String? content, String? reasoningContent});
}

/// @nodoc
class _$StreamingDeltaCopyWithImpl<$Res, $Val extends StreamingDelta>
    implements $StreamingDeltaCopyWith<$Res> {
  _$StreamingDeltaCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StreamingDelta
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? role = freezed,
    Object? content = freezed,
    Object? reasoningContent = freezed,
  }) {
    return _then(_value.copyWith(
      role: freezed == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      reasoningContent: freezed == reasoningContent
          ? _value.reasoningContent
          : reasoningContent // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StreamingDeltaImplCopyWith<$Res>
    implements $StreamingDeltaCopyWith<$Res> {
  factory _$$StreamingDeltaImplCopyWith(_$StreamingDeltaImpl value,
          $Res Function(_$StreamingDeltaImpl) then) =
      __$$StreamingDeltaImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? role, String? content, String? reasoningContent});
}

/// @nodoc
class __$$StreamingDeltaImplCopyWithImpl<$Res>
    extends _$StreamingDeltaCopyWithImpl<$Res, _$StreamingDeltaImpl>
    implements _$$StreamingDeltaImplCopyWith<$Res> {
  __$$StreamingDeltaImplCopyWithImpl(
      _$StreamingDeltaImpl _value, $Res Function(_$StreamingDeltaImpl) _then)
      : super(_value, _then);

  /// Create a copy of StreamingDelta
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? role = freezed,
    Object? content = freezed,
    Object? reasoningContent = freezed,
  }) {
    return _then(_$StreamingDeltaImpl(
      role: freezed == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      reasoningContent: freezed == reasoningContent
          ? _value.reasoningContent
          : reasoningContent // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StreamingDeltaImpl implements _StreamingDelta {
  const _$StreamingDeltaImpl({this.role, this.content, this.reasoningContent});

  factory _$StreamingDeltaImpl.fromJson(Map<String, dynamic> json) =>
      _$$StreamingDeltaImplFromJson(json);

  @override
  final String? role;
  @override
  final String? content;
  @override
  final String? reasoningContent;

  @override
  String toString() {
    return 'StreamingDelta(role: $role, content: $content, reasoningContent: $reasoningContent)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StreamingDeltaImpl &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.reasoningContent, reasoningContent) ||
                other.reasoningContent == reasoningContent));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, role, content, reasoningContent);

  /// Create a copy of StreamingDelta
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StreamingDeltaImplCopyWith<_$StreamingDeltaImpl> get copyWith =>
      __$$StreamingDeltaImplCopyWithImpl<_$StreamingDeltaImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StreamingDeltaImplToJson(
      this,
    );
  }
}

abstract class _StreamingDelta implements StreamingDelta {
  const factory _StreamingDelta(
      {final String? role,
      final String? content,
      final String? reasoningContent}) = _$StreamingDeltaImpl;

  factory _StreamingDelta.fromJson(Map<String, dynamic> json) =
      _$StreamingDeltaImpl.fromJson;

  @override
  String? get role;
  @override
  String? get content;
  @override
  String? get reasoningContent;

  /// Create a copy of StreamingDelta
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StreamingDeltaImplCopyWith<_$StreamingDeltaImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
