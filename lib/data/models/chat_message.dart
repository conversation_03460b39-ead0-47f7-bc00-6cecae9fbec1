import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_message.freezed.dart';
part 'chat_message.g.dart';

@freezed
class ChatMessage with _$ChatMessage {
  const factory ChatMessage({
    required String role,
    required String content,
  }) = _ChatMessage;

  factory ChatMessage.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageFromJson(json);
}

@freezed
class ChatCompletionRequest with _$ChatCompletionRequest {
  const factory ChatCompletionRequest({
    required String model,
    required List<ChatMessage> messages,
    @Default(false) bool stream,
    int? maxTokens,
    @Default(false) bool enableThinking,
    int? thinkingBudget,
    Map<String, dynamic>? responseFormat,
  }) = _ChatCompletionRequest;

  factory ChatCompletionRequest.fromJson(Map<String, dynamic> json) =>
      _$ChatCompletionRequestFromJson(json);
}

@freezed
class ChatCompletionResponse with _$ChatCompletionResponse {
  const factory ChatCompletionResponse({
    required String id,
    required String object,
    required int created,
    required String model,
    required List<ChatChoice> choices,
  }) = _ChatCompletionResponse;

  factory ChatCompletionResponse.fromJson(Map<String, dynamic> json) =>
      _$ChatCompletionResponseFromJson(json);
}

@freezed
class ChatChoice with _$ChatChoice {
  const factory ChatChoice({
    required int index,
    required ChatMessage message,
    String? finishReason,
  }) = _ChatChoice;

  factory ChatChoice.fromJson(Map<String, dynamic> json) =>
      _$ChatChoiceFromJson(json);
}

@freezed
class StreamingChatChoice with _$StreamingChatChoice {
  const factory StreamingChatChoice({
    required int index,
    required StreamingDelta delta,
    String? finishReason,
  }) = _StreamingChatChoice;

  factory StreamingChatChoice.fromJson(Map<String, dynamic> json) =>
      _$StreamingChatChoiceFromJson(json);
}

@freezed
class StreamingDelta with _$StreamingDelta {
  const factory StreamingDelta({
    String? role,
    String? content,
    String? reasoningContent,
  }) = _StreamingDelta;

  factory StreamingDelta.fromJson(Map<String, dynamic> json) =>
      _$StreamingDeltaFromJson(json);
}
