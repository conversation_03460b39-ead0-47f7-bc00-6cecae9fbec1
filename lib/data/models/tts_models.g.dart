// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tts_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TTSRequestImpl _$$TTSRequestImplFromJson(Map<String, dynamic> json) =>
    _$TTSRequestImpl(
      model: json['model'] as String,
      input: json['input'] as String,
      voice: json['voice'] as String?,
      responseFormat: json['responseFormat'] as String? ?? 'mp3',
      sampleRate: (json['sampleRate'] as num?)?.toInt() ?? 32000,
      stream: json['stream'] as bool? ?? true,
      speed: (json['speed'] as num?)?.toDouble() ?? 1.0,
      gain: (json['gain'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$TTSRequestImplToJson(_$TTSRequestImpl instance) =>
    <String, dynamic>{
      'model': instance.model,
      'input': instance.input,
      'voice': instance.voice,
      'responseFormat': instance.responseFormat,
      'sampleRate': instance.sampleRate,
      'stream': instance.stream,
      'speed': instance.speed,
      'gain': instance.gain,
    };

_$TTSResultImpl _$$TTSResultImplFromJson(Map<String, dynamic> json) =>
    _$TTSResultImpl(
      audioData: (json['audioData'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      format: json['format'] as String,
      sampleRate: (json['sampleRate'] as num).toInt(),
      filePath: json['filePath'] as String?,
    );

Map<String, dynamic> _$$TTSResultImplToJson(_$TTSResultImpl instance) =>
    <String, dynamic>{
      'audioData': instance.audioData,
      'format': instance.format,
      'sampleRate': instance.sampleRate,
      'filePath': instance.filePath,
    };

_$DialogueTTSStatusImpl _$$DialogueTTSStatusImplFromJson(
        Map<String, dynamic> json) =>
    _$DialogueTTSStatusImpl(
      turn: (json['turn'] as num).toInt(),
      speaker: json['speaker'] as String,
      content: json['content'] as String,
      gender: json['gender'] as String,
      state: $enumDecodeNullable(_$TTSStateEnumMap, json['state']) ??
          TTSState.pending,
      audioFilePath: json['audioFilePath'] as String?,
      error: json['error'] as String?,
      voice: $enumDecodeNullable(_$TTSVoiceEnumMap, json['voice']),
    );

Map<String, dynamic> _$$DialogueTTSStatusImplToJson(
        _$DialogueTTSStatusImpl instance) =>
    <String, dynamic>{
      'turn': instance.turn,
      'speaker': instance.speaker,
      'content': instance.content,
      'gender': instance.gender,
      'state': _$TTSStateEnumMap[instance.state]!,
      'audioFilePath': instance.audioFilePath,
      'error': instance.error,
      'voice': _$TTSVoiceEnumMap[instance.voice],
    };

const _$TTSStateEnumMap = {
  TTSState.pending: 'pending',
  TTSState.generating: 'generating',
  TTSState.ready: 'ready',
  TTSState.playing: 'playing',
  TTSState.completed: 'completed',
  TTSState.error: 'error',
};

const _$TTSVoiceEnumMap = {
  TTSVoice.alexMale: 'fnlp/MOSS-TTSD-v0.5:alex',
  TTSVoice.annaFemale: 'fnlp/MOSS-TTSD-v0.5:anna',
  TTSVoice.bellaFemale: 'fnlp/MOSS-TTSD-v0.5:bella',
  TTSVoice.benjaminMale: 'fnlp/MOSS-TTSD-v0.5:benjamin',
  TTSVoice.charlesMale: 'fnlp/MOSS-TTSD-v0.5:charles',
  TTSVoice.claireFemale: 'fnlp/MOSS-TTSD-v0.5:claire',
  TTSVoice.davidMale: 'fnlp/MOSS-TTSD-v0.5:david',
  TTSVoice.dianaFemale: 'fnlp/MOSS-TTSD-v0.5:diana',
};
