// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ChatMessageImpl _$$ChatMessageImplFromJson(Map<String, dynamic> json) =>
    _$ChatMessageImpl(
      role: json['role'] as String,
      content: json['content'] as String,
    );

Map<String, dynamic> _$$ChatMessageImplToJson(_$ChatMessageImpl instance) =>
    <String, dynamic>{
      'role': instance.role,
      'content': instance.content,
    };

_$ChatCompletionRequestImpl _$$ChatCompletionRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$ChatCompletionRequestImpl(
      model: json['model'] as String,
      messages: (json['messages'] as List<dynamic>)
          .map((e) => ChatMessage.fromJson(e as Map<String, dynamic>))
          .toList(),
      stream: json['stream'] as bool? ?? false,
      maxTokens: (json['maxTokens'] as num?)?.toInt(),
      enableThinking: json['enableThinking'] as bool? ?? false,
      thinkingBudget: (json['thinkingBudget'] as num?)?.toInt(),
      responseFormat: json['responseFormat'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$ChatCompletionRequestImplToJson(
        _$ChatCompletionRequestImpl instance) =>
    <String, dynamic>{
      'model': instance.model,
      'messages': instance.messages,
      'stream': instance.stream,
      'maxTokens': instance.maxTokens,
      'enableThinking': instance.enableThinking,
      'thinkingBudget': instance.thinkingBudget,
      'responseFormat': instance.responseFormat,
    };

_$ChatCompletionResponseImpl _$$ChatCompletionResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ChatCompletionResponseImpl(
      id: json['id'] as String,
      object: json['object'] as String,
      created: (json['created'] as num).toInt(),
      model: json['model'] as String,
      choices: (json['choices'] as List<dynamic>)
          .map((e) => ChatChoice.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ChatCompletionResponseImplToJson(
        _$ChatCompletionResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'object': instance.object,
      'created': instance.created,
      'model': instance.model,
      'choices': instance.choices,
    };

_$ChatChoiceImpl _$$ChatChoiceImplFromJson(Map<String, dynamic> json) =>
    _$ChatChoiceImpl(
      index: (json['index'] as num).toInt(),
      message: ChatMessage.fromJson(json['message'] as Map<String, dynamic>),
      finishReason: json['finishReason'] as String?,
    );

Map<String, dynamic> _$$ChatChoiceImplToJson(_$ChatChoiceImpl instance) =>
    <String, dynamic>{
      'index': instance.index,
      'message': instance.message,
      'finishReason': instance.finishReason,
    };

_$StreamingChatChoiceImpl _$$StreamingChatChoiceImplFromJson(
        Map<String, dynamic> json) =>
    _$StreamingChatChoiceImpl(
      index: (json['index'] as num).toInt(),
      delta: StreamingDelta.fromJson(json['delta'] as Map<String, dynamic>),
      finishReason: json['finishReason'] as String?,
    );

Map<String, dynamic> _$$StreamingChatChoiceImplToJson(
        _$StreamingChatChoiceImpl instance) =>
    <String, dynamic>{
      'index': instance.index,
      'delta': instance.delta,
      'finishReason': instance.finishReason,
    };

_$StreamingDeltaImpl _$$StreamingDeltaImplFromJson(Map<String, dynamic> json) =>
    _$StreamingDeltaImpl(
      role: json['role'] as String?,
      content: json['content'] as String?,
      reasoningContent: json['reasoningContent'] as String?,
    );

Map<String, dynamic> _$$StreamingDeltaImplToJson(
        _$StreamingDeltaImpl instance) =>
    <String, dynamic>{
      'role': instance.role,
      'content': instance.content,
      'reasoningContent': instance.reasoningContent,
    };
