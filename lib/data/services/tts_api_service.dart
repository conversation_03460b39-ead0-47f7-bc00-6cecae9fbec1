import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/tts_models.dart';
import '../../core/exceptions/api_exceptions.dart';

/// SiliconFlow TTS API服务类
class TTSApiService {
  static const String _baseUrl = 'https://api.siliconflow.cn/v1';
  static const String _speechEndpoint = '/audio/speech';
  
  final Dio _dio;
  String? _apiKey;

  TTSApiService({Dio? dio}) : _dio = dio ?? Dio() {
    _setupDio();
  }

  void _setupDio() {
    _dio.options.baseUrl = _baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 60);
    _dio.options.sendTimeout = const Duration(seconds: 30);
    
    // 添加请求拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        if (_apiKey != null) {
          options.headers['Authorization'] = 'Bearer $_apiKey';
        }
        options.headers['Content-Type'] = 'application/json';
        handler.next(options);
      },
      onError: (error, handler) {
        final apiException = _handleDioError(error);
        handler.reject(DioException(
          requestOptions: error.requestOptions,
          error: apiException,
        ));
      },
    ));
  }

  /// 设置API密钥
  void setApiKey(String apiKey) {
    _apiKey = apiKey;
  }

  /// 创建文本转语音请求
  Future<TTSResult> createSpeech({
    required String text,
    TTSVoice? voice,
    TTSModel model = TTSModel.mossTTSD,
    AudioFormat format = AudioFormat.mp3,
    int sampleRate = 32000,
    double speed = 1.0,
    double gain = 0.0,
  }) async {
    if (_apiKey == null) {
      throw const UnauthorizedError(message: 'API密钥未设置');
    }

    try {
      final request = TTSRequest(
        model: model.value,
        input: text,
        voice: voice?.value,
        responseFormat: format.value,
        sampleRate: sampleRate,
        stream: false, // 暂时不使用流式
        speed: speed,
        gain: gain,
      );

      final response = await _dio.post(
        _speechEndpoint,
        data: request.toJson(),
        options: Options(
          responseType: ResponseType.bytes,
        ),
      );

      if (response.statusCode == 200) {
        final audioData = response.data as List<int>;
        
        // 保存音频文件到临时目录
        final filePath = await _saveAudioFile(audioData, format.value);
        
        return TTSResult(
          audioData: audioData,
          format: format.value,
          sampleRate: sampleRate,
          filePath: filePath,
        );
      } else {
        throw ServerError(
          message: '语音生成失败: ${response.statusCode}',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is ApiException) {
        rethrow;
      }
      throw _handleDioError(e);
    } catch (e) {
      throw UnknownError(message: '语音生成时发生未知错误: $e');
    }
  }

  /// 批量生成语音
  Future<List<TTSResult>> createBatchSpeech({
    required List<String> texts,
    required List<TTSVoice> voices,
    TTSModel model = TTSModel.mossTTSD,
    AudioFormat format = AudioFormat.mp3,
    int sampleRate = 32000,
    double speed = 1.0,
    double gain = 0.0,
  }) async {
    if (texts.length != voices.length) {
      throw const ValidationError(message: '文本数量与音色数量不匹配');
    }

    final results = <TTSResult>[];
    
    for (int i = 0; i < texts.length; i++) {
      try {
        final result = await createSpeech(
          text: texts[i],
          voice: voices[i],
          model: model,
          format: format,
          sampleRate: sampleRate,
          speed: speed,
          gain: gain,
        );
        results.add(result);
      } catch (e) {
        // 记录错误但继续处理其他文本
        print('生成第${i + 1}个语音时出错: $e');
        rethrow;
      }
    }
    
    return results;
  }

  /// 保存音频文件到临时目录
  Future<String> _saveAudioFile(List<int> audioData, String format) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final fileName = 'tts_${DateTime.now().millisecondsSinceEpoch}.$format';
      final filePath = path.join(tempDir.path, fileName);
      
      final file = File(filePath);
      await file.writeAsBytes(audioData);
      
      return filePath;
    } catch (e) {
      throw UnknownError(message: '保存音频文件失败: $e');
    }
  }

  /// 清理临时音频文件
  Future<void> cleanupTempFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final files = tempDir.listSync();
      
      for (final file in files) {
        if (file is File && file.path.contains('tts_')) {
          await file.delete();
        }
      }
    } catch (e) {
      print('清理临时文件时出错: $e');
    }
  }

  /// 处理Dio错误
  ApiException _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const NetworkError(message: '请求超时，请检查网络连接');
      
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data?.toString() ?? '未知错误';
        
        switch (statusCode) {
          case 400:
            return ValidationError(message: '请求参数错误: $message');
          case 401:
            return const UnauthorizedError(message: 'API密钥无效或已过期');
          case 404:
            return const NotFoundError(message: '请求的资源不存在');
          case 429:
            return const RateLimitError(message: '请求频率过高，请稍后重试');
          case 503:
            return const ServerError(message: '服务暂时不可用，请稍后重试');
          default:
            return ServerError(
              message: '服务器错误: $message',
              statusCode: statusCode,
            );
        }
      
      case DioExceptionType.cancel:
        return const NetworkError(message: '请求已取消');
      
      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          return const NetworkError(message: '网络连接失败，请检查网络设置');
        }
        return UnknownError(message: '未知网络错误: ${error.message}');
      
      default:
        return UnknownError(message: '网络请求失败: ${error.message}');
    }
  }

  /// 验证API密钥
  Future<bool> validateApiKey() async {
    if (_apiKey == null) return false;
    
    try {
      // 使用一个简单的测试请求来验证API密钥
      await createSpeech(text: 'test');
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取支持的音色列表
  List<TTSVoice> getSupportedVoices() {
    return TTSVoice.values;
  }

  /// 获取支持的模型列表
  List<TTSModel> getSupportedModels() {
    return TTSModel.values;
  }

  /// 获取支持的音频格式列表
  List<AudioFormat> getSupportedFormats() {
    return AudioFormat.values;
  }
}
