import 'dart:async';
import 'dart:io';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';
import '../models/tts_models.dart';

/// 音频播放状态
enum AudioPlaybackState {
  idle,     // 空闲
  loading,  // 加载中
  playing,  // 播放中
  paused,   // 暂停
  stopped,  // 停止
  completed,// 播放完成
  error,    // 错误
}

/// 音频播放管理器
class AudioPlayerManager extends ChangeNotifier {
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  AudioPlaybackState _state = AudioPlaybackState.idle;
  String? _currentFilePath;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  String? _error;
  
  // 播放队列相关
  List<DialogueTTSStatus> _playQueue = [];
  int _currentIndex = -1;
  bool _isAutoPlay = true;
  
  // 流控制器
  StreamSubscription<Duration>? _durationSubscription;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<PlayerState>? _playerStateSubscription;

  AudioPlayerManager() {
    _initializePlayer();
  }

  // Getters
  AudioPlaybackState get state => _state;
  String? get currentFilePath => _currentFilePath;
  Duration get duration => _duration;
  Duration get position => _position;
  String? get error => _error;
  List<DialogueTTSStatus> get playQueue => List.unmodifiable(_playQueue);
  int get currentIndex => _currentIndex;
  bool get isAutoPlay => _isAutoPlay;
  bool get hasNext => _currentIndex < _playQueue.length - 1;
  bool get hasPrevious => _currentIndex > 0;
  DialogueTTSStatus? get currentItem => 
      _currentIndex >= 0 && _currentIndex < _playQueue.length 
          ? _playQueue[_currentIndex] 
          : null;

  void _initializePlayer() {
    // 监听播放状态变化
    _playerStateSubscription = _audioPlayer.onPlayerStateChanged.listen((state) {
      switch (state) {
        case PlayerState.stopped:
          _updateState(AudioPlaybackState.stopped);
          break;
        case PlayerState.playing:
          _updateState(AudioPlaybackState.playing);
          break;
        case PlayerState.paused:
          _updateState(AudioPlaybackState.paused);
          break;
        case PlayerState.completed:
          _updateState(AudioPlaybackState.completed);
          _onPlaybackCompleted();
          break;
        case PlayerState.disposed:
          _updateState(AudioPlaybackState.idle);
          break;
      }
    });

    // 监听播放位置变化
    _positionSubscription = _audioPlayer.onPositionChanged.listen((position) {
      _position = position;
      notifyListeners();
    });

    // 监听音频时长变化
    _durationSubscription = _audioPlayer.onDurationChanged.listen((duration) {
      _duration = duration;
      notifyListeners();
    });
  }

  /// 播放指定文件
  Future<void> playFile(String filePath) async {
    try {
      _updateState(AudioPlaybackState.loading);
      _currentFilePath = filePath;
      _error = null;

      if (!File(filePath).existsSync()) {
        throw Exception('音频文件不存在: $filePath');
      }

      await _audioPlayer.play(DeviceFileSource(filePath));
    } catch (e) {
      _error = e.toString();
      _updateState(AudioPlaybackState.error);
      print('播放音频文件失败: $e');
    }
  }

  /// 暂停播放
  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      _error = e.toString();
      _updateState(AudioPlaybackState.error);
      print('暂停播放失败: $e');
    }
  }

  /// 恢复播放
  Future<void> resume() async {
    try {
      await _audioPlayer.resume();
    } catch (e) {
      _error = e.toString();
      _updateState(AudioPlaybackState.error);
      print('恢复播放失败: $e');
    }
  }

  /// 停止播放
  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      _position = Duration.zero;
      _currentFilePath = null;
    } catch (e) {
      _error = e.toString();
      _updateState(AudioPlaybackState.error);
      print('停止播放失败: $e');
    }
  }

  /// 跳转到指定位置
  Future<void> seekTo(Duration position) async {
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      _error = e.toString();
      print('跳转播放位置失败: $e');
    }
  }

  /// 设置播放队列
  void setPlayQueue(List<DialogueTTSStatus> queue) {
    _playQueue = List.from(queue);
    _currentIndex = -1;
    notifyListeners();
  }

  /// 添加到播放队列
  void addToQueue(DialogueTTSStatus item) {
    _playQueue.add(item);
    notifyListeners();
  }

  /// 清空播放队列
  void clearQueue() {
    _playQueue.clear();
    _currentIndex = -1;
    notifyListeners();
  }

  /// 播放队列中的指定项
  Future<void> playQueueItem(int index) async {
    if (index < 0 || index >= _playQueue.length) {
      throw ArgumentError('无效的队列索引: $index');
    }

    final item = _playQueue[index];
    if (item.audioFilePath == null) {
      throw Exception('音频文件路径为空');
    }

    _currentIndex = index;
    
    // 更新当前项状态为播放中
    _playQueue[index] = item.copyWith(state: TTSState.playing);
    
    await playFile(item.audioFilePath!);
    notifyListeners();
  }

  /// 播放下一个
  Future<void> playNext() async {
    if (hasNext) {
      await playQueueItem(_currentIndex + 1);
    }
  }

  /// 播放上一个
  Future<void> playPrevious() async {
    if (hasPrevious) {
      await playQueueItem(_currentIndex - 1);
    }
  }

  /// 设置自动播放
  void setAutoPlay(bool autoPlay) {
    _isAutoPlay = autoPlay;
    notifyListeners();
  }

  /// 播放完成回调
  void _onPlaybackCompleted() {
    if (_currentIndex >= 0 && _currentIndex < _playQueue.length) {
      // 更新当前项状态为完成
      _playQueue[_currentIndex] = _playQueue[_currentIndex].copyWith(
        state: TTSState.completed,
      );
    }

    // 如果开启自动播放且有下一个，则播放下一个
    if (_isAutoPlay && hasNext) {
      Future.delayed(const Duration(milliseconds: 500), () {
        playNext();
      });
    }
    
    notifyListeners();
  }

  /// 更新播放状态
  void _updateState(AudioPlaybackState newState) {
    if (_state != newState) {
      _state = newState;
      notifyListeners();
    }
  }

  /// 获取播放进度百分比
  double get progress {
    if (_duration.inMilliseconds == 0) return 0.0;
    return _position.inMilliseconds / _duration.inMilliseconds;
  }

  /// 格式化时间显示
  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  void dispose() {
    _durationSubscription?.cancel();
    _positionSubscription?.cancel();
    _playerStateSubscription?.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }
}
