import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/tts_models.dart';
import 'tts_api_service.dart';
import 'audio_player_manager.dart';

/// 对话TTS服务类
class DialogueTTSService extends ChangeNotifier {
  final TTSApiService _ttsApiService;
  final AudioPlayerManager _audioPlayerManager;
  
  // 对话TTS状态列表
  List<DialogueTTSStatus> _dialogueStatuses = [];
  
  // 说话者音色映射 (speaker -> voice)
  final Map<String, TTSVoice> _speakerVoiceMap = {};
  
  // 生成进度
  int _generatedCount = 0;
  int _totalCount = 0;
  bool _isGenerating = false;
  String? _error;

  DialogueTTSService({
    required TTSApiService ttsApiService,
    required AudioPlayerManager audioPlayerManager,
  }) : _ttsApiService = ttsApiService,
       _audioPlayerManager = audioPlayerManager;

  // Getters
  List<DialogueTTSStatus> get dialogueStatuses => List.unmodifiable(_dialogueStatuses);
  Map<String, TTSVoice> get speakerVoiceMap => Map.unmodifiable(_speakerVoiceMap);
  int get generatedCount => _generatedCount;
  int get totalCount => _totalCount;
  bool get isGenerating => _isGenerating;
  String? get error => _error;
  double get progress => _totalCount > 0 ? _generatedCount / _totalCount : 0.0;

  /// 从对话流程生成语音
  Future<void> generateSpeechFromDialogueFlow(Map<String, dynamic> dialogueData) async {
    try {
      _isGenerating = true;
      _error = null;
      _generatedCount = 0;
      _dialogueStatuses.clear();
      _speakerVoiceMap.clear();
      notifyListeners();

      // 解析参与者信息
      final participants = dialogueData['participants'] as List<dynamic>? ?? [];
      _assignVoicesToParticipants(participants);

      // 解析对话流程
      final dialogueFlow = dialogueData['dialogue_flow'] as List<dynamic>? ?? [];
      _totalCount = dialogueFlow.length;
      
      if (_totalCount == 0) {
        throw Exception('对话流程为空');
      }

      // 创建对话状态列表
      for (final turn in dialogueFlow) {
        final turnData = turn as Map<String, dynamic>;
        final speaker = turnData['speaker'] as String? ?? '';
        final content = turnData['content'] as String? ?? '';
        final turnNumber = turnData['turn'] as int? ?? 0;
        
        // 从参与者信息中获取性别
        final gender = _getGenderFromParticipants(participants, speaker);
        
        final status = DialogueTTSStatus(
          turn: turnNumber,
          speaker: speaker,
          content: content,
          gender: gender,
          state: TTSState.pending,
          voice: _speakerVoiceMap[speaker],
        );
        
        _dialogueStatuses.add(status);
      }
      
      notifyListeners();

      // 逐个生成语音
      for (int i = 0; i < _dialogueStatuses.length; i++) {
        await _generateSingleSpeech(i);
        _generatedCount = i + 1;
        notifyListeners();
      }

      // 设置播放队列
      _audioPlayerManager.setPlayQueue(_dialogueStatuses);
      
    } catch (e) {
      _error = e.toString();
      print('生成对话语音失败: $e');
    } finally {
      _isGenerating = false;
      notifyListeners();
    }
  }

  /// 生成单个对话的语音
  Future<void> _generateSingleSpeech(int index) async {
    if (index < 0 || index >= _dialogueStatuses.length) return;

    try {
      // 更新状态为生成中
      _dialogueStatuses[index] = _dialogueStatuses[index].copyWith(
        state: TTSState.generating,
      );
      notifyListeners();

      final status = _dialogueStatuses[index];
      final voice = status.voice ?? TTSVoice.getVoiceByGender(status.gender);

      // 调用TTS API生成语音
      final result = await _ttsApiService.createSpeech(
        text: status.content,
        voice: voice,
        model: TTSModel.mossTTSD,
        format: AudioFormat.mp3,
      );

      // 更新状态为就绪
      _dialogueStatuses[index] = status.copyWith(
        state: TTSState.ready,
        audioFilePath: result.filePath,
        voice: voice,
      );

    } catch (e) {
      // 更新状态为错误
      _dialogueStatuses[index] = _dialogueStatuses[index].copyWith(
        state: TTSState.error,
        error: e.toString(),
      );
      print('生成第${index + 1}个对话语音失败: $e');
    }
  }

  /// 为参与者分配音色
  void _assignVoicesToParticipants(List<dynamic> participants) {
    final maleVoices = TTSVoice.maleVoices.toList();
    final femaleVoices = TTSVoice.femaleVoices.toList();
    
    int maleIndex = 0;
    int femaleIndex = 0;

    for (final participant in participants) {
      final participantData = participant as Map<String, dynamic>;
      final name = participantData['name'] as String? ?? '';
      final gender = participantData['gender'] as String? ?? '';
      
      if (name.isNotEmpty) {
        TTSVoice voice;
        
        if (gender == '女' || gender.toLowerCase() == 'female') {
          voice = femaleVoices[femaleIndex % femaleVoices.length];
          femaleIndex++;
        } else {
          voice = maleVoices[maleIndex % maleVoices.length];
          maleIndex++;
        }
        
        _speakerVoiceMap[name] = voice;
        
        // 也为带有角色标识的说话者分配音色
        final roleId = participantData['role_id'] as String? ?? '';
        if (roleId.isNotEmpty) {
          final speakerWithRole = '$name($roleId)';
          _speakerVoiceMap[speakerWithRole] = voice;
        }
      }
    }
  }

  /// 从参与者信息中获取性别
  String _getGenderFromParticipants(List<dynamic> participants, String speaker) {
    for (final participant in participants) {
      final participantData = participant as Map<String, dynamic>;
      final name = participantData['name'] as String? ?? '';
      final roleId = participantData['role_id'] as String? ?? '';
      final gender = participantData['gender'] as String? ?? '男';
      
      // 匹配说话者名称
      if (speaker.contains(name) || speaker.contains(roleId)) {
        return gender;
      }
    }
    
    // 默认返回男性
    return '男';
  }

  /// 开始播放对话语音
  Future<void> startPlayback() async {
    if (_dialogueStatuses.isEmpty) {
      throw Exception('没有可播放的对话');
    }

    // 找到第一个准备就绪的对话
    final readyIndex = _dialogueStatuses.indexWhere(
      (status) => status.state == TTSState.ready && status.audioFilePath != null,
    );

    if (readyIndex == -1) {
      throw Exception('没有准备就绪的语音文件');
    }

    await _audioPlayerManager.playQueueItem(readyIndex);
  }

  /// 重新生成指定对话的语音
  Future<void> regenerateSpeech(int index) async {
    if (index < 0 || index >= _dialogueStatuses.length) return;

    await _generateSingleSpeech(index);
    
    // 更新播放队列
    _audioPlayerManager.setPlayQueue(_dialogueStatuses);
  }

  /// 重新生成所有语音
  Future<void> regenerateAllSpeech() async {
    _generatedCount = 0;
    _isGenerating = true;
    _error = null;
    notifyListeners();

    try {
      for (int i = 0; i < _dialogueStatuses.length; i++) {
        await _generateSingleSpeech(i);
        _generatedCount = i + 1;
        notifyListeners();
      }

      // 更新播放队列
      _audioPlayerManager.setPlayQueue(_dialogueStatuses);
      
    } catch (e) {
      _error = e.toString();
      print('重新生成所有语音失败: $e');
    } finally {
      _isGenerating = false;
      notifyListeners();
    }
  }

  /// 清理资源
  void cleanup() {
    _dialogueStatuses.clear();
    _speakerVoiceMap.clear();
    _generatedCount = 0;
    _totalCount = 0;
    _isGenerating = false;
    _error = null;
    _audioPlayerManager.clearQueue();
    notifyListeners();
  }

  /// 获取指定说话者的音色
  TTSVoice? getVoiceForSpeaker(String speaker) {
    return _speakerVoiceMap[speaker];
  }

  /// 设置说话者音色
  void setSpeakerVoice(String speaker, TTSVoice voice) {
    _speakerVoiceMap[speaker] = voice;
    
    // 更新对应的对话状态
    for (int i = 0; i < _dialogueStatuses.length; i++) {
      if (_dialogueStatuses[i].speaker.contains(speaker)) {
        _dialogueStatuses[i] = _dialogueStatuses[i].copyWith(voice: voice);
      }
    }
    
    notifyListeners();
  }

  /// 获取生成统计信息
  Map<String, int> getGenerationStats() {
    final stats = <String, int>{
      'total': _dialogueStatuses.length,
      'pending': 0,
      'generating': 0,
      'ready': 0,
      'error': 0,
    };

    for (final status in _dialogueStatuses) {
      switch (status.state) {
        case TTSState.pending:
          stats['pending'] = (stats['pending'] ?? 0) + 1;
          break;
        case TTSState.generating:
          stats['generating'] = (stats['generating'] ?? 0) + 1;
          break;
        case TTSState.ready:
          stats['ready'] = (stats['ready'] ?? 0) + 1;
          break;
        case TTSState.error:
          stats['error'] = (stats['error'] ?? 0) + 1;
          break;
        default:
          break;
      }
    }

    return stats;
  }
}
