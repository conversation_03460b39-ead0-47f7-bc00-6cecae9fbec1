import 'package:dio/dio.dart';
import '../../core/network/api_exception.dart';
import '../../core/network/dio_error_handler.dart';
import '../../core/network/siliconflow_dio_client.dart';
import '../models/chat_message.dart';

class SiliconFlowApiService {
  final SiliconFlowDioClient _dioClient;

  SiliconFlowApiService() : _dioClient = SiliconFlowDioClient();

  /// 设置 API Key
  void setApiKey(String apiKey) {
    _dioClient.instance.options.headers['Authorization'] = 'Bearer $apiKey';
  }

  /// 创建聊天补全请求
  Future<ChatCompletionResponse> createChatCompletion({
    required String model,
    required List<ChatMessage> messages,
    bool stream = false,
    int? maxTokens,
    bool enableThinking = false,
    int? thinkingBudget,
    Map<String, dynamic>? responseFormat,
  }) async {
    try {
      final request = ChatCompletionRequest(
        model: model,
        messages: messages,
        stream: stream,
        maxTokens: maxTokens,
        enableThinking: enableThinking,
        thinkingBudget: thinkingBudget,
        responseFormat: responseFormat,
      );

      final response = await _dioClient.instance.post(
        '/chat/completions',
        data: request.toJson(),
      );

      return ChatCompletionResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw DioErrorHandler.handleError(e);
    } catch (e) {
      throw UnknownError(message: '创建聊天补全时发生未知错误: $e');
    }
  }

  /// 流式聊天补全
  Future<Response> createStreamingChatCompletion({
    required String model,
required List<ChatMessage> messages,
    int? maxTokens,
    int? thinkingBudget,
    Map<String, dynamic>? responseFormat,
  }) async {
    try {
      final request = ChatCompletionRequest(
        model: model,
        messages: messages,
        stream: true,
        maxTokens: maxTokens,
        enableThinking: false,
        thinkingBudget: thinkingBudget,
        responseFormat: responseFormat,
      );

      return await _dioClient.instance.post(
        '/chat/completions',
        data: request.toJson(),
        options: Options(
          responseType: ResponseType.stream,
        ),
      );
    } on DioException catch (e) {
      throw DioErrorHandler.handleError(e);
    } catch (e) {
      throw UnknownError(message: '创建流式聊天补全时发生未知错误: $e');
    }
  }

  /// 获取可用模型列表
  Future<List<String>> getAvailableModels() async {
    try {
      final response = await _dioClient.instance.get('/models');

      if (response.data is Map<String, dynamic> &&
          response.data['data'] is List) {
        return (response.data['data'] as List)
            .map((model) => model['id']?.toString() ?? '')
            .where((id) => id.isNotEmpty)
            .toList();
      }

      return [];
    } on DioException catch (e) {
      throw DioErrorHandler.handleError(e);
    } catch (e) {
      throw UnknownError(message: '获取模型列表时发生未知错误: $e');
    }
  }

  /// 检查 API 密钥有效性
  Future<bool> validateApiKey() async {
    try {
      await _dioClient.instance.get('/models');
      return true;
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return false;
      }
      rethrow;
    } catch (e) {
      return false;
    }
  }
}
