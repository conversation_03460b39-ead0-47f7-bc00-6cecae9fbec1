import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';

import '../../core/network/api_exception.dart';
import '../../core/prompts/prompt_templates.dart';
import '../../core/services/storage_keys.dart';
import '../../core/services/storage_service.dart';
import '../../core/services/system_prompt_service.dart';
import '../models/chat_message.dart';
import '../services/siliconflow_api_service.dart';

class ChatRepository {
  final SiliconFlowApiService _apiService;
  final SystemPromptService _systemPromptService;

  ChatRepository(this._apiService, this._systemPromptService);

  /// 设置 API Key
  void setApiKey(String apiKey) {
    _apiService.setApiKey(apiKey);
  }

  /// 创建聊天补全
  Future<String> createChatCompletion({
    required String model,
    required List<ChatMessage> messages,
    int? maxTokens,
    int? thinkingBudget,
    Map<String, dynamic>? responseFormat,
  }) async {
    try {
      final response = await _apiService.createChatCompletion(
        model: model,
        messages: messages,
        maxTokens: maxTokens,
        thinkingBudget: thinkingBudget,
        responseFormat: responseFormat,
      );

      // 保存聊天记录
      await _saveChatHistory(messages, response.choices.first.message.content);

      return response.choices.first.message.content;
    } on ApiException {
      rethrow;
    } catch (e) {
      throw UnknownError(message: '处理聊天补全时发生错误: $e');
    }
  }

  /// 创建流式聊天补全
  Stream<String> createStreamingChatCompletion({
    required String model,
    required List<ChatMessage> messages,
    int? maxTokens,
    int? thinkingBudget,
    Map<String, dynamic>? responseFormat,
  }) async* {
    try {
      final response = await _apiService.createStreamingChatCompletion(
        model: model,
        messages: messages,
        maxTokens: maxTokens,
        thinkingBudget: thinkingBudget,
        responseFormat: responseFormat,
      );

      if (response.statusCode == 200 && response.data is ResponseBody) {
        final responseBody = response.data as ResponseBody;
        final completer = Completer<String>();
        final fullContent = StringBuffer();

        await for (final List<int> chunk in responseBody.stream) {
          final String decodedChunk = utf8.decode(chunk);
          final lines = decodedChunk.split('\n');
          for (final line in lines) {
            if (line.startsWith('data: ')) {
              final data = line.substring(6);
              if (data == '[DONE]') {
                completer.complete(fullContent.toString());
                continue;
              }

              try {
                final jsonData = jsonDecode(data);
                final content = jsonData['choices']?[0]?['delta']?['content'];
                if (content != null) {
                  fullContent.write(content);
                  yield content;
                }
              } catch (e) {
                // 忽略解析错误，继续处理下一个chunk
              }
            }
          }
        }

        // 保存完整的聊天记录
        if (fullContent.isNotEmpty) {
          await _saveChatHistory(messages, fullContent.toString());
        }
      } else {
        throw ServerError(
          message: '流式响应失败: ${response.statusCode}',
          statusCode: response.statusCode,
        );
      }
    } on ApiException {
      rethrow;
    } catch (e) {
      throw UnknownError(message: '处理流式聊天补全时发生错误: $e');
    }
  }

  /// 获取可用模型列表
  Future<List<String>> getAvailableModels() async {
    try {
      return await _apiService.getAvailableModels();
    } on ApiException {
      rethrow;
    } catch (e) {
      throw UnknownError(message: '获取模型列表时发生错误: $e');
    }
  }

  /// 验证 API 密钥有效性
  Future<bool> validateApiKey() async {
    try {
      return await _apiService.validateApiKey();
    } catch (e) {
      return false;
    }
  }

  /// 获取聊天历史记录
  Future<List<Map<String, dynamic>>> getChatHistory() async {
    try {
      final historyJson = StorageService.getString(StorageKeys.chatHistory);
      if (historyJson.isEmpty) return [];

      final List<dynamic> historyList = jsonDecode(historyJson);
      return historyList.cast<Map<String, dynamic>>();
    } catch (e) {
      return [];
    }
  }

  /// 清除聊天历史记录
  Future<void> clearChatHistory() async {
    await StorageService.remove(StorageKeys.chatHistory);
  }

  /// 保存聊天记录到本地存储
  Future<void> _saveChatHistory(
    List<ChatMessage> messages,
    String response,
  ) async {
    try {
      final history = await getChatHistory();

      final newEntry = {
        'timestamp': DateTime.now().toIso8601String(),
        'messages': messages.map((m) => m.toJson()).toList(),
        'response': response,
      };

      history.add(newEntry);

      // 只保留最近50条记录
      if (history.length > 50) {
        history.removeRange(0, history.length - 50);
      }

      await StorageService.setString(
        StorageKeys.chatHistory,
        jsonEncode(history),
      );
    } catch (e) {
      // 保存失败不影响主流程
      // 在生产环境中应该使用日志系统而不是print
    }
  }
}
