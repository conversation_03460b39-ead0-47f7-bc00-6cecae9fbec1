import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../data/models/chat_message.dart';
import '../data/models/tts_models.dart';
import '../data/services/dialogue_tts_service.dart';
import '../data/services/audio_player_manager.dart';
import 'audio_wave_animation.dart';

/// 增强的聊天消息组件，支持TTS功能
class EnhancedChatMessage extends StatefulWidget {
  final ChatMessage message;
  final bool isUser;
  final int messageIndex;
  final VoidCallback? onTTSToggle;

  const EnhancedChatMessage({
    super.key,
    required this.message,
    required this.isUser,
    required this.messageIndex,
    this.onTTSToggle,
  });

  @override
  State<EnhancedChatMessage> createState() => _EnhancedChatMessageState();
}

class _EnhancedChatMessageState extends State<EnhancedChatMessage> {
  bool _isExpanded = false;
  List<DialogueTTSStatus>? _dialogueItems;
  bool _hasTTSContent = false;

  @override
  void initState() {
    super.initState();
    _checkForTTSContent();
  }

  void _checkForTTSContent() {
    if (!widget.isUser && widget.message.content.contains('dialogue_flow')) {
      try {
        // 尝试解析JSON内容
        final content = widget.message.content;
        if (content.contains('{') && content.contains('}')) {
          _hasTTSContent = true;
        }
      } catch (e) {
        // 解析失败，不是JSON格式
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<DialogueTTSService, AudioPlayerManager>(
      builder: (context, ttsService, audioManager, child) {
        // 获取当前消息对应的对话状态
        final dialogueStatuses = ttsService.dialogueStatuses;
        final currentPlayingIndex = audioManager.currentIndex;
        
        return Align(
          alignment: widget.isUser ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 4),
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.85,
            ),
            child: Column(
              crossAxisAlignment: widget.isUser 
                  ? CrossAxisAlignment.end 
                  : CrossAxisAlignment.start,
              children: [
                // 主消息容器
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: widget.isUser
                        ? Theme.of(context).primaryColor
                        : Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 消息内容
                      if (!_hasTTSContent || !_isExpanded)
                        Text(
                          _hasTTSContent && !_isExpanded 
                              ? '🎭 圆桌会议对话已生成'
                              : widget.message.content,
                          style: TextStyle(
                            color: widget.isUser ? Colors.white : Colors.black87,
                          ),
                        ),
                      
                      // TTS控制按钮
                      if (_hasTTSContent) ...[
                        const SizedBox(height: 8),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // 展开/收起按钮
                            IconButton(
                              icon: Icon(
                                _isExpanded ? Icons.expand_less : Icons.expand_more,
                                color: widget.isUser ? Colors.white : Colors.black54,
                                size: 20,
                              ),
                              onPressed: () {
                                setState(() {
                                  _isExpanded = !_isExpanded;
                                });
                              },
                              constraints: const BoxConstraints(
                                minWidth: 32,
                                minHeight: 32,
                              ),
                              padding: EdgeInsets.zero,
                            ),
                            
                            // TTS状态指示器
                            if (ttsService.isGenerating)
                              const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            else if (dialogueStatuses.isNotEmpty)
                              AudioStatusIndicator(
                                isPlaying: audioManager.state == AudioPlaybackState.playing,
                                isLoading: audioManager.state == AudioPlaybackState.loading,
                                hasError: audioManager.state == AudioPlaybackState.error,
                                size: 20,
                                color: widget.isUser ? Colors.white : null,
                                onTap: () => _handleTTSToggle(ttsService, audioManager),
                              ),
                            
                            // 生成进度
                            if (ttsService.isGenerating) ...[
                              const SizedBox(width: 8),
                              Text(
                                '${ttsService.generatedCount}/${ttsService.totalCount}',
                                style: TextStyle(
                                  color: widget.isUser ? Colors.white70 : Colors.black54,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
                
                // 展开的对话列表
                if (_hasTTSContent && _isExpanded)
                  _buildDialogueList(ttsService, audioManager),
                
                // 音频控制面板
                if (_hasTTSContent && dialogueStatuses.isNotEmpty && audioManager.currentItem != null)
                  _buildAudioControlPanel(audioManager),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDialogueList(DialogueTTSService ttsService, AudioPlayerManager audioManager) {
    final dialogueStatuses = ttsService.dialogueStatuses;
    
    if (dialogueStatuses.isEmpty) {
      return Container(
        margin: const EdgeInsets.only(top: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text('正在解析对话内容...'),
      );
    }

    return Container(
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // 对话列表头部
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.record_voice_over, size: 16),
                const SizedBox(width: 8),
                Text(
                  '对话语音 (${dialogueStatuses.length}段)',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                if (ttsService.isGenerating)
                  Text(
                    '生成中... ${(ttsService.progress * 100).toInt()}%',
                    style: const TextStyle(fontSize: 12),
                  ),
              ],
            ),
          ),
          
          // 对话项列表
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: dialogueStatuses.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final status = dialogueStatuses[index];
              final isCurrentPlaying = audioManager.currentIndex == index;
              
              return ListTile(
                dense: true,
                leading: _buildDialogueStatusIcon(status, isCurrentPlaying),
                title: Text(
                  status.speaker,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                subtitle: Text(
                  status.content,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(fontSize: 12),
                ),
                trailing: status.state == TTSState.ready
                    ? IconButton(
                        icon: Icon(
                          isCurrentPlaying && audioManager.state == AudioPlaybackState.playing
                              ? Icons.pause
                              : Icons.play_arrow,
                          size: 20,
                        ),
                        onPressed: () => _playDialogueItem(audioManager, index),
                      )
                    : null,
                onTap: status.state == TTSState.ready
                    ? () => _playDialogueItem(audioManager, index)
                    : null,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDialogueStatusIcon(DialogueTTSStatus status, bool isCurrentPlaying) {
    switch (status.state) {
      case TTSState.pending:
        return const Icon(Icons.schedule, size: 16, color: Colors.grey);
      case TTSState.generating:
        return const SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(strokeWidth: 2),
        );
      case TTSState.ready:
        if (isCurrentPlaying) {
          return const AudioWaveAnimation(
            isPlaying: true,
            size: 16,
            barCount: 3,
          );
        }
        return const Icon(Icons.check_circle, size: 16, color: Colors.green);
      case TTSState.playing:
        return const AudioWaveAnimation(
          isPlaying: true,
          size: 16,
          barCount: 3,
        );
      case TTSState.completed:
        return const Icon(Icons.check_circle_outline, size: 16, color: Colors.blue);
      case TTSState.error:
        return const Icon(Icons.error, size: 16, color: Colors.red);
    }
  }

  Widget _buildAudioControlPanel(AudioPlayerManager audioManager) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade300,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 当前播放信息
          Row(
            children: [
              const Icon(Icons.volume_up, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  audioManager.currentItem?.speaker ?? '',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Text(
                '${audioManager.currentIndex + 1}/${audioManager.playQueue.length}',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // 进度条
          AudioProgressBar(
            progress: audioManager.progress,
            position: audioManager.position,
            duration: audioManager.duration,
            onSeek: (value) {
              final position = audioManager.duration * value;
              audioManager.seekTo(position);
            },
          ),
          
          const SizedBox(height: 8),
          
          // 控制按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: const Icon(Icons.skip_previous),
                onPressed: audioManager.hasPrevious ? audioManager.playPrevious : null,
              ),
              IconButton(
                icon: Icon(
                  audioManager.state == AudioPlaybackState.playing
                      ? Icons.pause
                      : Icons.play_arrow,
                ),
                onPressed: () => _handlePlayPause(audioManager),
              ),
              IconButton(
                icon: const Icon(Icons.stop),
                onPressed: audioManager.stop,
              ),
              IconButton(
                icon: const Icon(Icons.skip_next),
                onPressed: audioManager.hasNext ? audioManager.playNext : null,
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _handleTTSToggle(DialogueTTSService ttsService, AudioPlayerManager audioManager) {
    if (audioManager.state == AudioPlaybackState.playing) {
      audioManager.pause();
    } else if (audioManager.state == AudioPlaybackState.paused) {
      audioManager.resume();
    } else {
      ttsService.startPlayback();
    }
  }

  void _playDialogueItem(AudioPlayerManager audioManager, int index) {
    if (audioManager.currentIndex == index && 
        audioManager.state == AudioPlaybackState.playing) {
      audioManager.pause();
    } else {
      audioManager.playQueueItem(index);
    }
  }

  void _handlePlayPause(AudioPlayerManager audioManager) {
    if (audioManager.state == AudioPlaybackState.playing) {
      audioManager.pause();
    } else if (audioManager.state == AudioPlaybackState.paused) {
      audioManager.resume();
    }
  }
}
