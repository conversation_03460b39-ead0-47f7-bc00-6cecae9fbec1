import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../data/models/chat_message.dart';
import '../data/models/tts_models.dart';
import '../data/services/dialogue_tts_service.dart';
import '../data/services/audio_player_manager.dart';
import 'audio_wave_animation.dart';

/// 增强的聊天消息组件，支持TTS功能
class EnhancedChatMessage extends StatefulWidget {
  final ChatMessage message;
  final bool isUser;
  final int messageIndex;
  final VoidCallback? onTTSToggle;

  const EnhancedChatMessage({
    super.key,
    required this.message,
    required this.isUser,
    required this.messageIndex,
    this.onTTSToggle,
  });

  @override
  State<EnhancedChatMessage> createState() => _EnhancedChatMessageState();
}

class _EnhancedChatMessageState extends State<EnhancedChatMessage> {
  bool _isExpanded = false;
  List<DialogueTTSStatus>? _dialogueItems;
  bool _hasTTSContent = false;

  @override
  void initState() {
    super.initState();
    _checkForTTSContent();
  }

  void _checkForTTSContent() {
    if (!widget.isUser && widget.message.content.contains('dialogue_flow')) {
      try {
        // 尝试解析JSON内容
        final content = widget.message.content;
        if (content.contains('{') && content.contains('}')) {
          _hasTTSContent = true;
          // 解析对话项用于显示
          final dialogueData = jsonDecode(content);
          final dialogueFlow = dialogueData['dialogue_flow'] as List?;
          if (dialogueFlow != null) {
            _dialogueItems = dialogueFlow.map((item) => DialogueTTSStatus(
              turn: item['turn'] ?? 0,
              speaker: item['speaker'] ?? '',
              content: item['content'] ?? '',
              gender: _extractGenderFromSpeaker(item['speaker'] ?? '', dialogueData),
              state: TTSState.pending,
            )).toList();
          }
        }
      } catch (e) {
        // 解析失败，不是JSON格式
      }
    }
  }

  String _extractGenderFromSpeaker(String speaker, Map<String, dynamic> dialogueData) {
    try {
      final participants = dialogueData['participants'] as List?;
      if (participants != null) {
        for (final participant in participants) {
          final name = participant['name'] as String?;
          if (name != null && speaker.contains(name)) {
            return participant['gender'] as String? ?? '男';
          }
        }
      }
    } catch (e) {
      // 解析失败，使用默认值
    }
    return '男'; // 默认值
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<DialogueTTSService, AudioPlayerManager>(
      builder: (context, ttsService, audioManager, child) {
        // 获取当前消息对应的对话状态
        final dialogueStatuses = ttsService.dialogueStatuses;
        final isCurrentMessagePlaying = _hasTTSContent &&
            dialogueStatuses.isNotEmpty &&
            audioManager.state == AudioPlaybackState.playing;

        return Align(
          alignment: widget.isUser ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 4),
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.85,
            ),
            child: _hasTTSContent
                ? _buildTTSMessage(context, ttsService, audioManager, isCurrentMessagePlaying)
                : _buildRegularMessage(context),
          ),
        );
      },
    );
  }

  /// 构建普通消息
  Widget _buildRegularMessage(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: widget.isUser
            ? Theme.of(context).primaryColor
            : Colors.grey.shade200,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        widget.message.content,
        style: TextStyle(
          color: widget.isUser ? Colors.white : Colors.black87,
        ),
      ),
    );
  }

  /// 构建TTS消息
  Widget _buildTTSMessage(BuildContext context, DialogueTTSService ttsService,
      AudioPlayerManager audioManager, bool isCurrentMessagePlaying) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: widget.isUser
            ? Theme.of(context).primaryColor
            : Colors.grey.shade200,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 消息头部：标题和TTS控制
          Row(
            children: [
              // 消息标题
              Expanded(
                child: Text(
                  '🎭 圆桌会议对话',
                  style: TextStyle(
                    color: widget.isUser ? Colors.white : Colors.black87,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              // TTS控制区域
              _buildTTSControls(ttsService, audioManager, isCurrentMessagePlaying),
            ],
          ),

          const SizedBox(height: 8),

          // 对话列表
          if (_dialogueItems != null && _dialogueItems!.isNotEmpty)
            _buildCompactDialogueList(ttsService, audioManager),

          // 音频播放控制条
          if (isCurrentMessagePlaying || audioManager.currentItem != null)
            _buildAudioProgressBar(audioManager),
        ],
      ),
    );
  }

  /// 构建TTS控制区域
  Widget _buildTTSControls(DialogueTTSService ttsService, AudioPlayerManager audioManager,
      bool isCurrentMessagePlaying) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 生成进度指示器
        if (ttsService.isGenerating)
          Container(
            width: 16,
            height: 16,
            margin: const EdgeInsets.only(right: 8),
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                widget.isUser ? Colors.white : Theme.of(context).primaryColor,
              ),
            ),
          ),

        // 播放/暂停按钮
        IconButton(
          icon: Icon(
            _getPlayButtonIcon(ttsService, audioManager),
            color: widget.isUser ? Colors.white : Colors.black54,
            size: 20,
          ),
          onPressed: () => _handleTTSToggle(ttsService, audioManager),
          constraints: const BoxConstraints(
            minWidth: 32,
            minHeight: 32,
          ),
          padding: EdgeInsets.zero,
        ),

        // 生成进度文本
        if (ttsService.isGenerating)
          Text(
            '${ttsService.generatedCount}/${ttsService.totalCount}',
            style: TextStyle(
              color: widget.isUser ? Colors.white70 : Colors.black54,
              fontSize: 10,
            ),
          ),
      ],
    );
  }

  /// 构建紧凑的对话列表
  Widget _buildCompactDialogueList(DialogueTTSService ttsService, AudioPlayerManager audioManager) {
    final dialogueStatuses = ttsService.dialogueStatuses;

    return Column(
      children: [
        // 对话项列表
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: dialogueStatuses.length,
          separatorBuilder: (context, index) => const SizedBox(height: 4),
          itemBuilder: (context, index) {
            final status = dialogueStatuses[index];
            final isCurrentPlaying = audioManager.currentIndex == index &&
                audioManager.state == AudioPlaybackState.playing;

            return _buildCompactDialogueItem(status, index, isCurrentPlaying, audioManager);
          },
        ),
      ],
    );
  }

  /// 构建紧凑的对话项
  Widget _buildCompactDialogueItem(DialogueTTSStatus status, int index,
      bool isCurrentPlaying, AudioPlayerManager audioManager) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: isCurrentPlaying
            ? (widget.isUser ? Colors.white.withOpacity(0.2) : Colors.blue.withOpacity(0.1))
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // 状态指示器
          _buildDialogueStatusIcon(status, isCurrentPlaying),
          const SizedBox(width: 8),

          // 对话内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  status.speaker,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                    color: widget.isUser ? Colors.white : Colors.black87,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  status.content,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 11,
                    color: widget.isUser ? Colors.white70 : Colors.black54,
                  ),
                ),
              ],
            ),
          ),

          // 播放按钮
          if (status.state == TTSState.ready)
            IconButton(
              icon: Icon(
                isCurrentPlaying ? Icons.pause : Icons.play_arrow,
                size: 16,
                color: widget.isUser ? Colors.white : Colors.black54,
              ),
              onPressed: () => _playDialogueItem(audioManager, index),
              constraints: const BoxConstraints(
                minWidth: 24,
                minHeight: 24,
              ),
              padding: EdgeInsets.zero,
            ),
        ],
      ),
    );
  }

  /// 构建音频进度条
  Widget _buildAudioProgressBar(AudioPlayerManager audioManager) {
    if (audioManager.currentItem == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Column(
        children: [
          // 进度条
          Row(
            children: [
              Text(
                audioManager.formatDuration(audioManager.position),
                style: TextStyle(
                  fontSize: 10,
                  color: widget.isUser ? Colors.white70 : Colors.black54,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: LinearProgressIndicator(
                  value: audioManager.progress,
                  backgroundColor: widget.isUser
                      ? Colors.white.withOpacity(0.3)
                      : Colors.grey.shade300,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    widget.isUser ? Colors.white : Theme.of(context).primaryColor,
                  ),
                  minHeight: 2,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                audioManager.formatDuration(audioManager.duration),
                style: TextStyle(
                  fontSize: 10,
                  color: widget.isUser ? Colors.white70 : Colors.black54,
                ),
              ),
            ],
          ),

          // 音频波形动画（播放时显示）
          if (audioManager.state == AudioPlaybackState.playing)
            Container(
              margin: const EdgeInsets.only(top: 4),
              child: AudioWaveAnimation(
                isPlaying: true,
                size: 12,
                barCount: 5,
                color: widget.isUser ? Colors.white : Theme.of(context).primaryColor,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDialogueList(DialogueTTSService ttsService, AudioPlayerManager audioManager) {
    final dialogueStatuses = ttsService.dialogueStatuses;
    
    if (dialogueStatuses.isEmpty) {
      return Container(
        margin: const EdgeInsets.only(top: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text('正在解析对话内容...'),
      );
    }

    return Container(
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // 对话列表头部
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.record_voice_over, size: 16),
                const SizedBox(width: 8),
                Text(
                  '对话语音 (${dialogueStatuses.length}段)',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                if (ttsService.isGenerating)
                  Text(
                    '生成中... ${(ttsService.progress * 100).toInt()}%',
                    style: const TextStyle(fontSize: 12),
                  ),
              ],
            ),
          ),
          
          // 对话项列表
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: dialogueStatuses.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final status = dialogueStatuses[index];
              final isCurrentPlaying = audioManager.currentIndex == index &&
                  audioManager.state == AudioPlaybackState.playing;
              
              return ListTile(
                dense: true,
                leading: _buildDialogueStatusIcon(status, isCurrentPlaying),
                title: Text(
                  status.speaker,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                subtitle: Text(
                  status.content,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(fontSize: 12),
                ),
                trailing: status.state == TTSState.ready
                    ? IconButton(
                        icon: Icon(
                          isCurrentPlaying && audioManager.state == AudioPlaybackState.playing
                              ? Icons.pause
                              : Icons.play_arrow,
                          size: 20,
                        ),
                        onPressed: () => _playDialogueItem(audioManager, index),
                      )
                    : null,
                onTap: status.state == TTSState.ready
                    ? () => _playDialogueItem(audioManager, index)
                    : null,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDialogueStatusIcon(DialogueTTSStatus status, bool isCurrentPlaying) {
    switch (status.state) {
      case TTSState.pending:
        return const Icon(Icons.schedule, size: 16, color: Colors.grey);
      case TTSState.generating:
        return const SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(strokeWidth: 2),
        );
      case TTSState.ready:
        if (isCurrentPlaying) {
          return const AudioWaveAnimation(
            isPlaying: true,
            size: 16,
            barCount: 3,
          );
        }
        return const Icon(Icons.check_circle, size: 16, color: Colors.green);
      case TTSState.playing:
        return const AudioWaveAnimation(
          isPlaying: true,
          size: 16,
          barCount: 3,
        );
      case TTSState.completed:
        return const Icon(Icons.check_circle_outline, size: 16, color: Colors.blue);
      case TTSState.error:
        return const Icon(Icons.error, size: 16, color: Colors.red);
    }
  }

  Widget _buildAudioControlPanel(AudioPlayerManager audioManager) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade300,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 当前播放信息
          Row(
            children: [
              const Icon(Icons.volume_up, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  audioManager.currentItem?.speaker ?? '',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Text(
                '${audioManager.currentIndex + 1}/${audioManager.playQueue.length}',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // 进度条
          AudioProgressBar(
            progress: audioManager.progress,
            position: audioManager.position,
            duration: audioManager.duration,
            onSeek: (value) {
              final position = audioManager.duration * value;
              audioManager.seekTo(position);
            },
          ),
          
          const SizedBox(height: 8),
          
          // 控制按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: const Icon(Icons.skip_previous),
                onPressed: audioManager.hasPrevious ? audioManager.playPrevious : null,
              ),
              IconButton(
                icon: Icon(
                  audioManager.state == AudioPlaybackState.playing
                      ? Icons.pause
                      : Icons.play_arrow,
                ),
                onPressed: () => _handlePlayPause(audioManager),
              ),
              IconButton(
                icon: const Icon(Icons.stop),
                onPressed: audioManager.stop,
              ),
              IconButton(
                icon: const Icon(Icons.skip_next),
                onPressed: audioManager.hasNext ? audioManager.playNext : null,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 获取播放按钮图标
  IconData _getPlayButtonIcon(DialogueTTSService ttsService, AudioPlayerManager audioManager) {
    if (ttsService.isGenerating) {
      return Icons.hourglass_empty;
    } else if (audioManager.state == AudioPlaybackState.playing) {
      return Icons.pause;
    } else if (audioManager.state == AudioPlaybackState.paused) {
      return Icons.play_arrow;
    } else if (ttsService.dialogueStatuses.isNotEmpty) {
      return Icons.play_arrow;
    } else {
      return Icons.play_arrow;
    }
  }

  void _handleTTSToggle(DialogueTTSService ttsService, AudioPlayerManager audioManager) {
    if (audioManager.state == AudioPlaybackState.playing) {
      audioManager.pause();
    } else if (audioManager.state == AudioPlaybackState.paused) {
      audioManager.resume();
    } else if (ttsService.dialogueStatuses.isNotEmpty) {
      // 如果TTS已经生成，开始播放
      ttsService.startPlayback();
    } else if (_hasTTSContent) {
      // 如果有TTS内容但还未生成，尝试重新生成
      try {
        final dialogueData = jsonDecode(widget.message.content);
        ttsService.generateSpeechFromDialogueFlow(dialogueData);
      } catch (e) {
        print('重新生成TTS失败: $e');
      }
    }
  }

  void _playDialogueItem(AudioPlayerManager audioManager, int index) {
    if (audioManager.currentIndex == index && 
        audioManager.state == AudioPlaybackState.playing) {
      audioManager.pause();
    } else {
      audioManager.playQueueItem(index);
    }
  }

  void _handlePlayPause(AudioPlayerManager audioManager) {
    if (audioManager.state == AudioPlaybackState.playing) {
      audioManager.pause();
    } else if (audioManager.state == AudioPlaybackState.paused) {
      audioManager.resume();
    }
  }
}
