import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import '../../core/providers/locale_provider.dart';

extension AppLocalizationsExtension on AppLocalizations {
  static AppLocalizations of(BuildContext context) {
    return AppLocalizations.of(context)!;
  }
}

class LanguageSettingsPage extends StatelessWidget {
  const LanguageSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizationsExtension.of(context);
    
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          l10n.languageSettings,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: const Color(0xFF1E1E1E),
            fontWeight: FontWeight.w500,
          ),
        ),
        leading: I<PERSON><PERSON>utton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF1E1E1E)),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16.0),
            Text(
              l10n.selectLanguage,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: const Color(0xFF8A8A8E),
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(height: 16.0),
            Expanded(
              child: ListView(
                children: [
                  _buildLanguageOption(
                    context,
                    locale: const Locale('en'),
                    title: l10n.english,
                    subtitle: 'English (US)',
                    flag: '🇺🇸',
                  ),
                  const SizedBox(height: 12.0),
                  _buildLanguageOption(
                    context,
                    locale: const Locale('zh', 'TW'),
                    title: l10n.chineseTraditional,
                    subtitle: '中文 (繁體)',
                    flag: '🇭🇰',
                  ),
                  const SizedBox(height: 12.0),
                  _buildLanguageOption(
                    context,
                    locale: const Locale('ar'),
                    title: l10n.arabic,
                    subtitle: 'العربية',
                    flag: '🇸🇦',
                  ),
                  const SizedBox(height: 12.0),
                  _buildLanguageOption(
                    context,
                    locale: const Locale('hi'),
                    title: l10n.hindi,
                    subtitle: 'हिन्दी',
                    flag: '🇮🇳',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageOption(
    BuildContext context, {
    required Locale locale,
    required String title,
    required String subtitle,
    required String flag,
  }) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isSelected = localeProvider.locale?.languageCode == locale.languageCode &&
                      (locale.countryCode == null || localeProvider.locale?.countryCode == locale.countryCode);

    return GestureDetector(
      onTap: () async {
        await localeProvider.setLocale(locale);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizationsExtension.of(context).languageChanged),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      },
      child: Container(
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.0),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 20,
              offset: const Offset(0, 5),
            ),
          ],
          border: isSelected
              ? Border.all(
                  color: const Color(0xFFFF9F43),
                  width: 2.0,
                )
              : null,
        ),
        child: Row(
          children: [
            Text(
              flag,
              style: const TextStyle(fontSize: 24.0),
            ),
            const SizedBox(width: 16.0),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: const Color(0xFF1E1E1E),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4.0),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: const Color(0xFF8A8A8E),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: Color(0xFFFF9F43),
                size: 24.0,
              ),
          ],
        ),
      ),
    );
  }
}