import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import 'package:round_table/core/services/api_keys.dart';

import '../../core/services/service_locator.dart';
import '../../core/services/storage_keys.dart';
import '../../core/services/storage_service.dart';
import '../../data/models/chat_message.dart';
import '../../data/repositories/chat_repository.dart';
import '../../data/services/dialogue_tts_service.dart';
import '../../data/services/audio_player_manager.dart';
import '../../widgets/enhanced_chat_message.dart';

class ChatPage extends StatefulWidget {
  final List<ChatMessage>? initialMessages;
  final String? title;

  const ChatPage({
    super.key,
    this.initialMessages,
    this.title,
  });

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ChatRepository _chatRepository = getIt<ChatRepository>();

  List<ChatMessage> _messages = [];
  bool _isLoading = false;
  bool _isStreaming = false;
  String _apiKey = '';
  String _currentModel = 'Qwen/Qwen3-8B';
  final List<String> _availableModels = [
    'Qwen/Qwen3-8B',
    'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B',
  ];

  @override
  void initState() {
    super.initState();
    _loadApiKey();

    // 如果有初始消息，使用初始消息，否则加载历史记录
    if (widget.initialMessages != null && widget.initialMessages!.isNotEmpty) {
      _messages = List.from(widget.initialMessages!);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // _scrollToBottom();
        _initializeTTSIfNeeded();
      });
    } else {
      _loadChatHistory();
    }
  }

  Future<void> _loadApiKey() async {
    final apiKey = StorageService.getString(StorageKeys.siliconFlowApiKey);
    final currentModel = StorageService.getString(StorageKeys.currentModel);

    setState(() {
      if (apiKey.isEmpty) {
        _apiKey = ApiKeys.siliconFlowApiKey;
      } else {
        _apiKey = apiKey;
      }
      _currentModel =
          currentModel.isNotEmpty ? currentModel : 'Qwen/Qwen3-8B';
    });
  }

  Future<void> _loadChatHistory() async {
    final history = await _chatRepository.getChatHistory();
    if (history.isNotEmpty) {
      // 加载最近的聊天记录
      final recentChat = history.last;
      final messages = (recentChat['messages'] as List)
          .map((m) => ChatMessage.fromJson(m as Map<String, dynamic>))
          .toList();

      setState(() {
        _messages.clear();
        _messages.addAll(messages);
      });
    }
  }

  Future<void> _handleSendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || _apiKey.isEmpty) return;

    setState(() {
      _messages.add(ChatMessage(role: 'user', content: message));
      _messageController.clear();
      _isLoading = true;
    });

    _scrollToBottom();

    try {
      // 设置 API Key
      _chatRepository.setApiKey(_apiKey);

      // 创建用户消息
      final userMessage = ChatMessage(role: 'user', content: message);
      final messages = [
        ..._messages.where((m) => m.role != 'system'),
        userMessage
      ];

      // 获取响应
      final response = await _chatRepository.createChatCompletion(
        model: _currentModel,
        messages: messages,
        maxTokens: 2048,
      );

      setState(() {
        _messages.add(ChatMessage(role: 'assistant', content: response));
        _isLoading = false;
      });

      _scrollToBottom();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('错误: $e')),
      );
    }
  }

  Future<void> _handleStreamingMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || _apiKey.isEmpty) return;

    setState(() {
      _messages.add(ChatMessage(role: 'user', content: message));
      _messageController.clear();
      _isStreaming = true;
    });

    _scrollToBottom();

    try {
      // 设置 API Key
      _chatRepository.setApiKey(_apiKey);

      // 创建用户消息
      final userMessage = ChatMessage(role: 'user', content: message);
      final messages = [
        ..._messages.where((m) => m.role != 'system'),
        userMessage
      ];

      final responseBuffer = StringBuffer();
      final stream = _chatRepository.createStreamingChatCompletion(
        model: _currentModel,
        messages: messages,
        maxTokens: 2048,
      );

      await for (final chunk in stream) {
        responseBuffer.write(chunk);

        // 更新最后一条消息（流式响应）
        setState(() {
          if (_messages.isNotEmpty && _messages.last.role == 'assistant') {
            _messages.last = ChatMessage(
              role: 'assistant',
              content: responseBuffer.toString(),
            );
          } else {
            _messages.add(ChatMessage(role: 'assistant', content: chunk));
          }
        });
      }

      setState(() {
        _isStreaming = false;
      });

      _scrollToBottom();
    } catch (e) {
      setState(() {
        _isStreaming = false;
      });

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('流式响应错误: $e')),
      );
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _showApiKeyDialog() async {
    final controller = TextEditingController(text: _apiKey);

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.chat_api_config_title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: controller,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.input_api_key_label,
                hintText: AppLocalizations.of(context)!.input_api_key_hint,
              ),
              obscureText: true,
            ),
            const SizedBox(height: 16),
            const Text(
              '请从 https://cloud.siliconflow.cn/account/ak 获取您的 API Key',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(AppLocalizations.of(context)!.button_cancel),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, controller.text.trim()),
            child: Text(AppLocalizations.of(context)!.button_save),
          ),
        ],
      ),
    );

    if (result != null) {
      await StorageService.setString(StorageKeys.siliconFlowApiKey, result);
      setState(() {
        _apiKey = result;
      });
    }
  }

  Future<void> _showModelSelectionDialog() async {
    final selectedModel = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.chat_model_selection_title),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _availableModels.length,
            itemBuilder: (context, index) {
              final model = _availableModels[index];
              return RadioListTile<String>(
                title: Text(model.split('/').last),
                subtitle: Text(model),
                value: model,
                groupValue: _currentModel,
                onChanged: (value) => Navigator.pop(context, value),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(AppLocalizations.of(context)!.button_cancel),
          ),
        ],
      ),
    );

    if (selectedModel != null) {
      await StorageService.setString(StorageKeys.currentModel, selectedModel);
      setState(() {
        _currentModel = selectedModel;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<DialogueTTSService>.value(
          value: getIt<DialogueTTSService>(),
        ),
        ChangeNotifierProvider<AudioPlayerManager>.value(
          value: getIt<AudioPlayerManager>(),
        ),
      ],
      child: Scaffold(
      appBar: AppBar(
        title: Text(widget.title ?? AppLocalizations.of(context)!.chat_page_title),
        actions: [
          IconButton(
            icon: const Icon(Icons.key),
            onPressed: _showApiKeyDialog,
            tooltip: AppLocalizations.of(context)!.button_configure,
          ),
          IconButton(
            icon: const Icon(Icons.model_training),
            onPressed: _showModelSelectionDialog,
            tooltip: AppLocalizations.of(context)!.chat_model_selection_title,
          ),
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () async {
              await _chatRepository.clearChatHistory();
              setState(() {
                _messages.clear();
              });
            },
            tooltip: AppLocalizations.of(context)!.button_clear_chat,
          ),
        ],
      ),
      body: Column(
        children: [
          // API Key 状态提示
          if (_apiKey.isEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              color: Colors.orange.shade100,
              child: Row(
                children: [
                  const Icon(Icons.warning, color: Colors.orange),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      AppLocalizations.of(context)!.chat_api_config_message,
                      style: const TextStyle(color: Colors.orange),
                    ),
                  ),
                  TextButton(
                    onPressed: _showApiKeyDialog,
                    child: Text(
                        AppLocalizations.of(context)!.button_configure_api),
                  ),
                ],
              ),
            ),

          // 当前模型显示
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            color: Colors.grey.shade100,
            child: Row(
              children: [
                const Icon(Icons.model_training, size: 16),
                const SizedBox(width: 8),
                Text('当前模型: ${_currentModel.split('/').last}'),
              ],
            ),
          ),

          // 聊天消息列表
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount:
                  _messages.length + (_isLoading || _isStreaming ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == _messages.length) {
                  // 显示加载状态
                  return const Padding(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                final message = _messages[index];
                final isUser = message.role == 'user';

                return EnhancedChatMessage(
                  message: message,
                  isUser: isUser,
                  messageIndex: index,
                );
              },
            ),
          ),

          // 输入框
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.shade200,
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText:
                          AppLocalizations.of(context)!.input_message_hint,
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _isLoading || _isStreaming
                        ? null
                        : _handleSendMessage(),
                  ),
                ),
                const SizedBox(width: 8),
                if (!_isStreaming) ...[
                  IconButton(
                    onPressed: _isLoading ? null : _handleSendMessage,
                    icon: const Icon(Icons.send),
                    color: Theme.of(context).primaryColor,
                  ),
                  IconButton(
                    onPressed: _isLoading ? null : _handleStreamingMessage,
                    icon: const Icon(Icons.stream),
                    color: Theme.of(context).primaryColor,
                    tooltip: '流式响应',
                  ),
                ] else
                  IconButton(
                    onPressed: () {
                      // 取消流式响应
                      setState(() {
                        _isStreaming = false;
                      });
                    },
                    icon: const Icon(Icons.stop),
                    color: Colors.red,
                  ),
              ],
            ),
          ),
        ],
      ),
      ),
    );
  }

  /// 如果有初始消息包含TTS数据，则初始化TTS功能
  void _initializeTTSIfNeeded() {
    if (widget.initialMessages != null && widget.initialMessages!.isNotEmpty) {
      // 检查是否有包含对话数据的消息
      for (final message in widget.initialMessages!) {
        if (message.content.contains('dialogue_flow')) {
          // 延迟执行，确保TTS服务已经处理完数据
          Future.delayed(const Duration(milliseconds: 1000), () {
            _startTTSPlayback();
          });
          break;
        }
      }
    }
  }

  /// 开始TTS播放
  void _startTTSPlayback() {
    final ttsService = getIt<DialogueTTSService>();
    if (ttsService.dialogueStatuses.isNotEmpty) {
      ttsService.startPlayback();
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}

/// 聊天状态提供者
class ChatProvider extends ChangeNotifier {
  final ChatRepository _chatRepository = getIt<ChatRepository>();

  List<ChatMessage> _messages = [];
  bool _isLoading = false;
  String? _error;

  List<ChatMessage> get messages => _messages;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> sendMessage(String content) async {
    if (content.isEmpty) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final userMessage = ChatMessage(role: 'user', content: content);
      _messages.add(userMessage);

      final response = await _chatRepository.createChatCompletion(
        model: 'Qwen/Qwen3-8B',
        messages: _messages,
      );

      final assistantMessage =
          ChatMessage(role: 'assistant', content: response);
      _messages.add(assistantMessage);
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearMessages() {
    _messages.clear();
    _error = null;
    notifyListeners();
  }
}
