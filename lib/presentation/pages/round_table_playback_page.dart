import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/models/chat_message.dart';
import '../../data/models/tts_models.dart';
import '../../data/services/dialogue_tts_service.dart';
import '../../data/services/audio_player_manager.dart';
import '../../core/services/service_locator.dart';
import '../../widgets/audio_wave_animation.dart';

/// 圆桌会议播放页面 - 实现逐段显示和播放同步
class RoundTablePlaybackPage extends StatefulWidget {
  final List<ChatMessage> messages;
  final String title;

  const RoundTablePlaybackPage({
    super.key,
    required this.messages,
    required this.title,
  });

  @override
  State<RoundTablePlaybackPage> createState() => _RoundTablePlaybackPageState();
}

class _RoundTablePlaybackPageState extends State<RoundTablePlaybackPage> {
  final ScrollController _scrollController = ScrollController();
  final DialogueTTSService _ttsService = getIt<DialogueTTSService>();
  final AudioPlayerManager _audioManager = getIt<AudioPlayerManager>();
  
  List<DialogueTTSStatus> _displayedDialogues = [];
  int _currentDisplayIndex = -1;
  bool _isAutoPlaying = false;

  @override
  void initState() {
    super.initState();
    _initializePlayback();
    _setupAudioListener();
  }

  void _initializePlayback() {
    // 从消息中提取对话数据并初始化TTS
    if (widget.messages.isNotEmpty) {
      final lastMessage = widget.messages.last;
      if (lastMessage.content.contains('dialogue_flow')) {
        try {
          // 这里应该解析JSON数据，但为了简化，我们直接使用TTS服务的状态
          Future.delayed(const Duration(milliseconds: 500), () {
            if (_ttsService.dialogueStatuses.isNotEmpty) {
              _startSequentialPlayback();
            }
          });
        } catch (e) {
          print('初始化播放失败: $e');
        }
      }
    }
  }

  void _setupAudioListener() {
    _audioManager.addListener(_onAudioStateChanged);
  }

  void _onAudioStateChanged() {
    if (_audioManager.state == AudioPlaybackState.completed && _isAutoPlaying) {
      // 当前音频播放完成，显示下一个对话项
      _showNextDialogue();
    }
  }

  void _startSequentialPlayback() {
    setState(() {
      _isAutoPlaying = true;
      _displayedDialogues.clear();
      _currentDisplayIndex = -1;
    });
    
    _showNextDialogue();
  }

  void _showNextDialogue() {
    final allDialogues = _ttsService.dialogueStatuses;
    
    if (_currentDisplayIndex + 1 < allDialogues.length) {
      setState(() {
        _currentDisplayIndex++;
        _displayedDialogues.add(allDialogues[_currentDisplayIndex]);
      });
      
      // 滚动到底部
      Future.delayed(const Duration(milliseconds: 100), () {
        _scrollToBottom();
      });
      
      // 开始播放当前对话的语音
      if (allDialogues[_currentDisplayIndex].state == TTSState.ready) {
        _audioManager.playQueueItem(_currentDisplayIndex);
      }
    } else {
      // 所有对话都已显示完成
      setState(() {
        _isAutoPlaying = false;
      });
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  void dispose() {
    _audioManager.removeListener(_onAudioStateChanged);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<DialogueTTSService>.value(value: _ttsService),
        ChangeNotifierProvider<AudioPlayerManager>.value(value: _audioManager),
      ],
      child: Scaffold(
        appBar: AppBar(
          title: Text(widget.title),
          actions: [
            // 播放控制按钮
            Consumer<AudioPlayerManager>(
              builder: (context, audioManager, child) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 自动播放切换
                    IconButton(
                      icon: Icon(_isAutoPlaying ? Icons.pause : Icons.play_arrow),
                      onPressed: _toggleAutoPlay,
                      tooltip: _isAutoPlaying ? '暂停自动播放' : '开始自动播放',
                    ),
                    // 重新开始
                    IconButton(
                      icon: const Icon(Icons.replay),
                      onPressed: _restartPlayback,
                      tooltip: '重新开始',
                    ),
                  ],
                );
              },
            ),
          ],
        ),
        body: Column(
          children: [
            // 进度指示器
            _buildProgressIndicator(),
            
            // 对话显示区域
            Expanded(
              child: _buildDialogueList(),
            ),
            
            // 音频控制面板
            _buildAudioControlPanel(),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Consumer<DialogueTTSService>(
      builder: (context, ttsService, child) {
        final totalCount = ttsService.dialogueStatuses.length;
        final currentIndex = _currentDisplayIndex + 1;
        
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            border: Border(
              bottom: BorderSide(color: Colors.grey.shade300),
            ),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '对话进度',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  Text(
                    '$currentIndex / $totalCount',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: totalCount > 0 ? currentIndex / totalCount : 0.0,
                backgroundColor: Colors.grey.shade300,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDialogueList() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _displayedDialogues.length,
      itemBuilder: (context, index) {
        final dialogue = _displayedDialogues[index];
        final isCurrentPlaying = _audioManager.currentIndex == index;
        
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.only(bottom: 16),
          child: Card(
            elevation: isCurrentPlaying ? 4 : 1,
            color: isCurrentPlaying ? Colors.blue.shade50 : null,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 说话者信息
                  Row(
                    children: [
                      // 音频状态指示器
                      _buildAudioStatusIcon(dialogue, isCurrentPlaying),
                      const SizedBox(width: 12),
                      
                      // 说话者名称
                      Expanded(
                        child: Text(
                          dialogue.speaker,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isCurrentPlaying ? Colors.blue.shade700 : null,
                          ),
                        ),
                      ),
                      
                      // 轮次编号
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '第${dialogue.turn}轮',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // 对话内容
                  Text(
                    dialogue.content,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      height: 1.5,
                    ),
                  ),
                  
                  // 播放控制（仅当前项显示）
                  if (isCurrentPlaying && dialogue.state == TTSState.ready) ...[
                    const SizedBox(height: 12),
                    _buildDialogueControls(index),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAudioStatusIcon(DialogueTTSStatus dialogue, bool isCurrentPlaying) {
    switch (dialogue.state) {
      case TTSState.ready:
        if (isCurrentPlaying && _audioManager.state == AudioPlaybackState.playing) {
          return const AudioWaveAnimation(
            isPlaying: true,
            size: 20,
            barCount: 3,
            color: Colors.blue,
          );
        }
        return const Icon(Icons.volume_up, color: Colors.green, size: 20);
      case TTSState.playing:
        return const AudioWaveAnimation(
          isPlaying: true,
          size: 20,
          barCount: 3,
          color: Colors.blue,
        );
      case TTSState.completed:
        return const Icon(Icons.check_circle, color: Colors.blue, size: 20);
      case TTSState.generating:
        return const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        );
      case TTSState.error:
        return const Icon(Icons.error, color: Colors.red, size: 20);
      default:
        return const Icon(Icons.schedule, color: Colors.grey, size: 20);
    }
  }

  Widget _buildDialogueControls(int index) {
    return Consumer<AudioPlayerManager>(
      builder: (context, audioManager, child) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: Icon(
                audioManager.state == AudioPlaybackState.playing
                    ? Icons.pause
                    : Icons.play_arrow,
              ),
              onPressed: () => _togglePlayPause(index),
            ),
            IconButton(
              icon: const Icon(Icons.stop),
              onPressed: audioManager.stop,
            ),
          ],
        );
      },
    );
  }

  Widget _buildAudioControlPanel() {
    return Consumer<AudioPlayerManager>(
      builder: (context, audioManager, child) {
        if (audioManager.currentItem == null) {
          return const SizedBox.shrink();
        }
        
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.shade300,
                blurRadius: 4,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: AudioProgressBar(
            progress: audioManager.progress,
            position: audioManager.position,
            duration: audioManager.duration,
            onSeek: (value) {
              final position = audioManager.duration * value;
              audioManager.seekTo(position);
            },
          ),
        );
      },
    );
  }

  void _toggleAutoPlay() {
    if (_isAutoPlaying) {
      setState(() {
        _isAutoPlaying = false;
      });
      _audioManager.pause();
    } else {
      _startSequentialPlayback();
    }
  }

  void _restartPlayback() {
    _audioManager.stop();
    setState(() {
      _displayedDialogues.clear();
      _currentDisplayIndex = -1;
    });
    _startSequentialPlayback();
  }

  void _togglePlayPause(int index) {
    if (_audioManager.currentIndex == index) {
      if (_audioManager.state == AudioPlaybackState.playing) {
        _audioManager.pause();
      } else {
        _audioManager.resume();
      }
    } else {
      _audioManager.playQueueItem(index);
    }
  }
}
