import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import '../../core/services/document_parser_service.dart';
import '../../core/services/web_scraper_service.dart';
import 'content_preview_page.dart';

/// 内容输入页面 - 支持文件上传和链接输入
class ContentInputPage extends StatefulWidget {
  const ContentInputPage({super.key});

  @override
  State<ContentInputPage> createState() => _ContentInputPageState();
}

class _ContentInputPageState extends State<ContentInputPage> {
  final TextEditingController _urlController = TextEditingController();
  final DocumentParserService _documentParser = DocumentParserService();
  final WebScraperService _webScraper = WebScraperService();

  String? _selectedFilePath;
  String? _selectedFileName;
  bool _isFileSelected = false;
  bool _isUrlEntered = false;
  bool _isProcessing = false;
  String? _errorMessage;

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('添加内容'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 标题和说明
            Text(
              '选择内容来源',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '您可以上传文档文件或粘贴网页链接',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 32),

            // 文件上传区域
            _buildFileUploadArea(theme, colorScheme),
            const SizedBox(height: 24),

            // 分隔线
            Row(
              children: [
                Expanded(child: Divider(color: colorScheme.outline.withOpacity(0.3))),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    '或',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurface.withOpacity(0.5),
                    ),
                  ),
                ),
                Expanded(child: Divider(color: colorScheme.outline.withOpacity(0.3))),
              ],
            ),
            const SizedBox(height: 24),

            // 链接输入区域
            _buildUrlInputArea(theme, colorScheme),

            const Spacer(),

            // 错误信息
            if (_errorMessage != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _errorMessage!,
                  style: TextStyle(color: colorScheme.onErrorContainer),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // 继续按钮
            _buildContinueButton(theme, colorScheme),
          ],
        ),
      ),
    );
  }

  Widget _buildFileUploadArea(ThemeData theme, ColorScheme colorScheme) {
    final isHighlighted = _isFileSelected && !_isUrlEntered;
    
    return GestureDetector(
      onTap: _isProcessing ? null : _pickFile,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: isHighlighted 
              ? colorScheme.primaryContainer.withOpacity(0.3)
              : colorScheme.surface,
          border: Border.all(
            color: isHighlighted 
                ? colorScheme.primary 
                : colorScheme.outline.withOpacity(0.3),
            width: isHighlighted ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              _isFileSelected ? Icons.description : Icons.upload_file,
              size: 48,
              color: isHighlighted 
                  ? colorScheme.primary 
                  : colorScheme.onSurface.withOpacity(0.6),
            ),
            const SizedBox(height: 16),
            Text(
              _isFileSelected && _selectedFileName != null
                  ? _selectedFileName!
                  : '点击选择文件',
              style: theme.textTheme.titleMedium?.copyWith(
                color: isHighlighted 
                    ? colorScheme.primary 
                    : colorScheme.onSurface,
                fontWeight: _isFileSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _isFileSelected && _selectedFilePath != null
                  ? _selectedFilePath!
                  : '支持 PDF、Word、Markdown、文本文件',
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUrlInputArea(ThemeData theme, ColorScheme colorScheme) {
    final isHighlighted = _isUrlEntered && !_isFileSelected;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isHighlighted 
            ? colorScheme.primaryContainer.withOpacity(0.3)
            : colorScheme.surface,
        border: Border.all(
          color: isHighlighted 
              ? colorScheme.primary 
              : colorScheme.outline.withOpacity(0.3),
          width: isHighlighted ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.link,
                color: isHighlighted 
                    ? colorScheme.primary 
                    : colorScheme.onSurface.withOpacity(0.6),
              ),
              const SizedBox(width: 12),
              Text(
                '粘贴网页链接',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: isHighlighted 
                      ? colorScheme.primary 
                      : colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _urlController,
            enabled: !_isProcessing,
            onChanged: _onUrlChanged,
            decoration: InputDecoration(
              hintText: '请输入网页链接地址',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: colorScheme.outline.withOpacity(0.3)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: colorScheme.primary),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            keyboardType: TextInputType.url,
          ),
        ],
      ),
    );
  }

  Widget _buildContinueButton(ThemeData theme, ColorScheme colorScheme) {
    final canContinue = (_isFileSelected || _isUrlEntered) && !_isProcessing;

    return SizedBox(
      height: 56,
      child: ElevatedButton(
        onPressed: canContinue ? _processContinue : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: canContinue ? colorScheme.primary : colorScheme.outline.withOpacity(0.3),
          foregroundColor: canContinue ? colorScheme.onPrimary : colorScheme.onSurface.withOpacity(0.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
          elevation: canContinue ? 2 : 0,
        ),
        child: _isProcessing
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(colorScheme.onPrimary),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('继续'),
                  const SizedBox(width: 8),
                  Icon(Icons.arrow_forward, size: 20),
                ],
              ),
      ),
    );
  }

  /// 选择文件
  Future<void> _pickFile() async {
    try {
      setState(() {
        _errorMessage = null;
      });

      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: _documentParser.getSupportedExtensions(),
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        setState(() {
          _selectedFilePath = file.path;
          _selectedFileName = file.name;
          _isFileSelected = true;
          _isUrlEntered = false;
          _urlController.clear();
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = '文件选择失败: ${e.toString()}';
      });
    }
  }

  /// URL输入变化处理
  void _onUrlChanged(String value) {
    final trimmedValue = value.trim();
    setState(() {
      _isUrlEntered = trimmedValue.isNotEmpty && _isValidUrl(trimmedValue);
      if (_isUrlEntered) {
        _isFileSelected = false;
        _selectedFilePath = null;
        _selectedFileName = null;
      }
      _errorMessage = null;
    });
  }

  /// 验证URL格式
  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// 处理继续操作
  Future<void> _processContinue() async {
    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    try {
      String markdown;
      String title;

      if (_isFileSelected && _selectedFilePath != null) {
        // 处理文件上传
        final result = await _documentParser.parseDocument(_selectedFilePath!);
        if (!result.success) {
          throw Exception(result.error ?? '文件解析失败');
        }
        markdown = result.markdown;
        title = result.fileName;
      } else if (_isUrlEntered) {
        // 处理链接
        final url = _urlController.text.trim();
        final result = await _webScraper.scrapeWebPage(url);
        if (!result.success) {
          throw Exception(result.error ?? '网页抓取失败');
        }
        markdown = result.markdown;
        title = url;
      } else {
        throw Exception('请选择文件或输入链接');
      }

      // 跳转到预览页面
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ContentPreviewPage(
              title: title,
              markdown: markdown,
            ),
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }
}
