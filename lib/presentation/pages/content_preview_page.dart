import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import '../../data/models/chat_message.dart';
import 'chat_page.dart';
import 'round_table_playback_page.dart';
import 'round_table_service.dart';

/// 内容预览页面
class ContentPreviewPage extends StatefulWidget {
  final String title;
  final String markdown;

  const ContentPreviewPage({
    super.key,
    required this.title,
    required this.markdown,
  });

  @override
  State<ContentPreviewPage> createState() => _ContentPreviewPageState();
}

class _ContentPreviewPageState extends State<ContentPreviewPage> {
  final RoundTableService _roundTableService = RoundTableService();
  bool _isProcessing = false;
  bool _useDebugMode = false; // 调试模式开关

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('内容预览'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          // 调试模式切换按钮
          IconButton(
            icon: Icon(_useDebugMode ? Icons.bug_report : Icons.bug_report_outlined),
            onPressed: () {
              setState(() {
                _useDebugMode = !_useDebugMode;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(_useDebugMode ? '已启用调试模式' : '已关闭调试模式'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
            tooltip: _useDebugMode ? '关闭调试模式' : '启用调试模式',
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareContent(context),
          ),
        ],
      ),
      body: Column(
        children: [
          // 标题区域
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: colorScheme.outline.withOpacity(0.2),
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '内容来源',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withOpacity(0.6),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          // Markdown内容区域
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              child: widget.markdown.trim().isEmpty
                  ? _buildEmptyState(theme, colorScheme)
                  : _buildMarkdownContent(theme, colorScheme),
            ),
          ),

          // 底部操作按钮
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: colorScheme.surface,
              border: Border(
                top: BorderSide(
                  color: colorScheme.outline.withOpacity(0.2),
                ),
              ),
            ),
            child: Row(
              children: [
                // 返回按钮
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      side: BorderSide(color: colorScheme.outline),
                    ),
                    child: const Text('返回'),
                  ),
                ),
                const SizedBox(width: 16),
                // 继续按钮
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: _isProcessing ? null : () => _continueToNextStep(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isProcessing ? colorScheme.outline.withOpacity(0.3) : colorScheme.primary,
                      foregroundColor: _isProcessing ? colorScheme.onSurface.withOpacity(0.5) : colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: _isProcessing ? 0 : 2,
                    ),
                    child: _isProcessing
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                height: 18,
                                width: 18,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    colorScheme.onSurface.withOpacity(0.5),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Text('生成中...'),
                            ],
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text('继续'),
                              const SizedBox(width: 8),
                              Icon(Icons.arrow_forward, size: 18),
                            ],
                          ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme, ColorScheme colorScheme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.description_outlined,
            size: 64,
            color: colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            '暂无内容',
            style: theme.textTheme.titleMedium?.copyWith(
              color: colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '无法解析内容，请检查文件格式或链接是否正确',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMarkdownContent(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Markdown(
          data: widget.markdown,
          selectable: true,
          styleSheet: MarkdownStyleSheet(
            // 自定义Markdown样式
            h1: theme.textTheme.headlineMedium?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
            h2: theme.textTheme.headlineSmall?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
            h3: theme.textTheme.titleLarge?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
            p: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface,
              height: 1.6,
            ),
            code: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.primary,
              backgroundColor: colorScheme.primaryContainer.withOpacity(0.3),
              fontFamily: 'monospace',
            ),
            codeblockDecoration: BoxDecoration(
              color: colorScheme.surfaceVariant.withOpacity(0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            blockquoteDecoration: BoxDecoration(
              color: colorScheme.primaryContainer.withOpacity(0.2),
              border: Border(
                left: BorderSide(
                  color: colorScheme.primary,
                  width: 4,
                ),
              ),
            ),
            listBullet: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.primary,
            ),
          ),
          onTapLink: (text, href, title) {
            if (href != null) {
              _openLink(href);
            }
          },
        ),
      ),
    );
  }

  /// 分享内容
  void _shareContent(BuildContext context) {
    // 这里可以实现分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('分享功能开发中...'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// 继续到下一步
  Future<void> _continueToNextStep(BuildContext context) async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      List<ChatMessage> chatMessages;

      chatMessages = await _roundTableService.generateRoundTableWithTTS(
        articleContent: widget.markdown,
        title: widget.title,
      );

      if (!mounted) return;

      // 6. 导航跳转到圆桌会议播放页面，并传递生成的对话内容
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => ChatPage(
            initialMessages: chatMessages,
            title: '圆桌会议 - ${widget.title}',
          ),
        ),
      );

    } catch (e) {
      if (!mounted) return;

      // 错误处理
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('生成圆桌会议失败: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: '确定',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// 打开链接
  void _openLink(String url) {
    // 这里可以实现打开链接的功能
    // 可以使用 url_launcher 包
    debugPrint('打开链接: $url');
  }


}
