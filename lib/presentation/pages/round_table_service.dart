import 'dart:convert';
import '../../core/services/service_locator.dart';
import '../../data/repositories/chat_repository.dart';
import '../../data/models/chat_message.dart';
import '../../data/services/dialogue_tts_service.dart';
import '../../data/services/tts_api_service.dart';
import '../../core/services/system_prompt_service.dart';
import '../../core/services/storage_service.dart';
import '../../core/services/storage_keys.dart';

/// 圆桌会议服务
/// 
/// 负责处理从Markdown内容生成圆桌会议对话的完整流程
class RoundTableService {
  final ChatRepository _chatRepository = getIt<ChatRepository>();
  final SystemPromptService _systemPromptService = SystemPromptService();
  final DialogueTTSService _dialogueTTSService = getIt<DialogueTTSService>();
  final TTSApiService _ttsApiService = getIt<TTSApiService>();

  /// 生成圆桌会议对话
  ///
  /// [articleContent] Markdown格式的文章内容
  /// [title] 文章标题，用于生成会议主题
  ///
  /// 返回包含圆桌会议对话的ChatMessage列表
  Future<List<ChatMessage>> generateRoundTableDiscussion({
    required String articleContent,
    required String title,
  }) async {
    if (articleContent.trim().isEmpty) {
      throw Exception('文章内容不能为空');
    }

    // 1. 获取API Key
    final storageApiKey = StorageService.getString(StorageKeys.siliconFlowApiKey);
    final apiKey = storageApiKey.isEmpty ? 'sk-tgjkyezhlaoopfckhnpymngprlhongchkweqlcipdogvmsfe' : storageApiKey;
    if (apiKey.isEmpty) {
      throw Exception('请先在设置中配置API Key');
    }

    // 2. 构建包含系统提示词的消息列表
    final messages = _systemPromptService.buildChatMessages(
      templateId: 'round_table_meeting',
      messages: [
        ChatMessage(role: 'user', content: '请为以下内容生成圆桌会议对话'),
      ],
      variables: {
        'articleContent': articleContent,
      },
    );

    // 3. 发送LLM请求（带重试机制）
    _chatRepository.setApiKey(apiKey);

    String? response;
    int retryCount = 0;
    const maxRetries = 2;

    while (retryCount <= maxRetries) {
      try {
        // 使用更适合的模型和参数配置
        response = await _chatRepository.createChatCompletion(
          model: 'Qwen/Qwen3-8B', // 使用更稳定的模型
          messages: messages,
          maxTokens: 2048, // 减少token数量以加快响应
          responseFormat: {'type': 'json_object'},
        );
        break; // 成功则跳出循环
      } catch (e) {
        retryCount++;
        if (retryCount > maxRetries) {
          throw Exception('生成圆桌会议失败，已重试$maxRetries次: $e');
        }
        // 等待一段时间后重试
        await Future.delayed(Duration(seconds: retryCount * 2));
      }
    }

    if (response == null) {
      throw Exception('生成圆桌会议失败，未收到有效响应');
    }

    // 4. 解析响应并构建聊天消息
    final jsonResponse = _parseJsonResponse(response);
    return _buildChatMessagesFromResponse(jsonResponse, title);
  }

  /// 解析LLM返回的JSON响应
  Map<String, dynamic> _parseJsonResponse(String response) {
    try {
      // 尝试直接解析JSON
      return jsonDecode(response);
    } catch (e) {
      // 如果直接解析失败，尝试提取JSON部分
      final jsonMatch = RegExp(r'\{.*\}', dotAll: true).firstMatch(response);
      if (jsonMatch != null) {
        try {
          return jsonDecode(jsonMatch.group(0)!);
        } catch (e) {
          throw Exception('无法解析LLM响应中的JSON格式: $e');
        }
      }
      throw Exception('LLM响应中未找到有效的JSON格式');
    }
  }

  /// 从JSON响应构建聊天消息列表
  List<ChatMessage> _buildChatMessagesFromResponse(
    Map<String, dynamic> jsonResponse,
    String title,
  ) {
    final List<ChatMessage> messages = [];
    
    try {
      // 获取对话流程
      final dialogueFlow = jsonResponse['dialogue_flow'] as List<dynamic>?;
      if (dialogueFlow == null || dialogueFlow.isEmpty) {
        throw Exception('JSON响应中未找到dialogue_flow字段');
      }

      // 获取参与者信息
      final participants = jsonResponse['participants'] as List<dynamic>?;

      // 添加会议开始的系统消息
      final meetingTheme = jsonResponse['meeting_metadata']?['theme'] as String?;
      final systemMessage = _buildSystemMessage(meetingTheme ?? title, participants);
      messages.add(ChatMessage(role: 'system', content: systemMessage));

      // 转换对话流程为聊天消息
      for (final turn in dialogueFlow) {
        final speaker = turn['speaker'] as String?;
        final content = turn['content'] as String?;
        
        if (speaker != null && content != null) {
          // 格式化发言者信息
          final formattedContent = '$speaker:\n\n$content';
          
          messages.add(ChatMessage(
            role: 'assistant',
            content: formattedContent,
          ));
        }
      }

      if (messages.length <= 1) { // 只有系统消息
        throw Exception('未能从响应中提取到有效的对话内容');
      }

      return messages;
    } catch (e) {
      throw Exception('解析对话内容失败: $e');
    }
  }

  /// 构建系统消息
  String _buildSystemMessage(String theme, List<dynamic>? participants) {
    final buffer = StringBuffer();
    buffer.writeln('🎯 圆桌会议主题: $theme');
    buffer.writeln();
    
    if (participants != null && participants.isNotEmpty) {
      buffer.writeln('👥 参与者:');
      for (final participant in participants) {
        final name = participant['name'] as String?;
        final identity = participant['identity'] as String?;
        final perspective = participant['perspective'] as String?;
        
        if (name != null && identity != null) {
          buffer.writeln('• $name ($identity)');
          if (perspective != null && perspective.isNotEmpty) {
            buffer.writeln('  *观点立场:* $perspective');
          }
          buffer.writeln();
        }
      }
    }
    
    buffer.writeln('---');
    buffer.writeln('💬 会议开始');
    
    return buffer.toString().trim();
  }
}
