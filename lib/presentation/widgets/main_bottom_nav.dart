import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class MainBottomNav extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const MainBottomNav({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: onTap,
        backgroundColor: Colors.white,
        elevation: 0,
        type: BottomNavigationBarType.fixed,
        showSelectedLabels: false,
        showUnselectedLabels: false,
        selectedItemColor: const Color(0xFFFF9F43),
        unselectedItemColor: const Color(0xFF8A8A8E),
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.explore_outlined, size: 24.0),
            activeIcon: const Icon(Icons.explore, size: 24.0),
            label: AppLocalizations.of(context)!.nav_discover,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.history, size: 24.0),
            activeIcon: const Icon(Icons.history, size: 24.0),
            label: AppLocalizations.of(context)!.nav_history,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.settings_outlined, size: 24.0),
            activeIcon: const Icon(Icons.settings, size: 24.0),
            label: AppLocalizations.of(context)!.nav_management,
          ),
        ],
      ),
    );
  }
}
