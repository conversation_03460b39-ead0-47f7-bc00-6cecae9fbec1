import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:round_table/core/services/storage_service.dart';
import 'package:round_table/core/services/storage_keys.dart';
import 'package:round_table/core/providers/locale_provider.dart';
import 'package:round_table/presentation/pages/language_settings_page.dart';

void main() {
  group('Language Switch Tests', () {
    setUp(() async {
      // 初始化 SharedPreferences 的 mock
      SharedPreferences.setMockInitialValues({});
      WidgetsFlutterBinding.ensureInitialized();
      // 避免重复初始化 StorageService
      try {
        await StorageService.init();
      } catch (e) {
        // 如果已经初始化，忽略错误
      }
    });

    testWidgets('Language settings page displays correctly',
        (WidgetTester tester) async {
      // 创建一个简化的测试环境
      await tester.pumpWidget(
        ChangeNotifierProvider(
          create: (context) => LocaleProvider()..initialize(),
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: const LanguageSettingsPage(),
          ),
        ),
      );

      // 等待初始化完成
      await tester.pumpAndSettle();

      // 验证页面标题
      expect(find.text('Language Settings'), findsOneWidget);

      // 验证语言选项存在 - 只检查标题，不检查副标题
      expect(find.text('English'), findsWidgets);
      // 由于国际化，语言名称现在来自ARB文件，我们验证可以找到语言设置标题
      expect(find.text('Select Language'), findsOneWidget);

      // 验证国旗图标存在
      expect(find.text('🇺🇸'), findsOneWidget);
      expect(find.text('🇭🇰'), findsOneWidget);
      expect(find.text('🇸🇦'), findsOneWidget);
      expect(find.text('🇮🇳'), findsOneWidget);
    });

    testWidgets('Language selection changes locale',
        (WidgetTester tester) async {
      final localeProvider = LocaleProvider();
      await localeProvider.initialize();

      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: localeProvider,
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            locale: localeProvider.locale,
            home: const LanguageSettingsPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 初始语言应该是英语
      expect(localeProvider.locale, isNull);

      // 点击中文选项 - 使用第一个匹配的中文文本
      await tester.tap(find.textContaining('中文').first);
      await tester.pumpAndSettle();

      // 验证语言已切换
      expect(localeProvider.locale?.languageCode, 'zh');
      expect(localeProvider.locale?.countryCode, 'Hant');

      // 验证存储的值
      final savedLanguage = StorageService.getString(StorageKeys.userLanguage);
      expect(savedLanguage, 'zh-Hant');
    });

    testWidgets('Selected language shows checkmark',
        (WidgetTester tester) async {
      final localeProvider = LocaleProvider();
      await localeProvider.initialize();

      // 设置初始语言为阿拉伯语
      await localeProvider.setLocale(const Locale('ar'));

      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: localeProvider,
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            locale: localeProvider.locale,
            home: const LanguageSettingsPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证阿拉伯语选项有选中标记
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
    });
  });
}
