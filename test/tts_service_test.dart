import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:round_table/data/services/tts_api_service.dart';
import 'package:round_table/data/services/audio_player_manager.dart';
import 'package:round_table/data/services/dialogue_tts_service.dart';
import 'package:round_table/data/models/tts_models.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('TTS API Service Tests', () {
    late TTSApiService ttsApiService;

    setUp(() {
      ttsApiService = TTSApiService();
    });

    test('should create TTS request with correct parameters', () {
      final request = TTSRequest(
        model: 'fnlp/MOSS-TTSD-v0.5',
        input: 'Hello, this is a test.',
        voice: 'fnlp/MOSS-TTSD-v0.5:alex',
        responseFormat: 'mp3',
        sampleRate: 32000,
        speed: 1.0,
        gain: 0.0,
      );

      expect(request.model, equals('fnlp/MOSS-TTSD-v0.5'));
      expect(request.input, equals('Hello, this is a test.'));
      expect(request.voice, equals('fnlp/MOSS-TTSD-v0.5:alex'));
      expect(request.responseFormat, equals('mp3'));
      expect(request.sampleRate, equals(32000));
      expect(request.speed, equals(1.0));
      expect(request.gain, equals(0.0));
    });

    test('should get voice by gender correctly', () {
      final maleVoice = TTSVoice.getVoiceByGender('男');
      final femaleVoice = TTSVoice.getVoiceByGender('女');

      expect(TTSVoice.maleVoices.contains(maleVoice), isTrue);
      expect(TTSVoice.femaleVoices.contains(femaleVoice), isTrue);
    });

    test('should return supported voices', () {
      final voices = ttsApiService.getSupportedVoices();
      expect(voices.isNotEmpty, isTrue);
      expect(voices.length, equals(TTSVoice.values.length));
    });

    test('should return supported models', () {
      final models = ttsApiService.getSupportedModels();
      expect(models.isNotEmpty, isTrue);
      expect(models.contains(TTSModel.mossTTSD), isTrue);
    });

    test('should return supported formats', () {
      final formats = ttsApiService.getSupportedFormats();
      expect(formats.isNotEmpty, isTrue);
      expect(formats.contains(AudioFormat.mp3), isTrue);
      expect(formats.contains(AudioFormat.wav), isTrue);
    });
  });

  group('Audio Player Manager Tests', () {
    late AudioPlayerManager audioManager;

    setUp(() {
      audioManager = AudioPlayerManager();
    });

    tearDown(() {
      audioManager.dispose();
    });

    test('should initialize with idle state', () {
      expect(audioManager.state, equals(AudioPlaybackState.idle));
      expect(audioManager.currentFilePath, isNull);
      expect(audioManager.duration, equals(Duration.zero));
      expect(audioManager.position, equals(Duration.zero));
    });

    test('should handle play queue correctly', () {
      final testQueue = [
        const DialogueTTSStatus(
          turn: 1,
          speaker: 'Speaker 1',
          content: 'Test content 1',
          gender: '男',
          state: TTSState.ready,
          audioFilePath: '/test/path1.mp3',
        ),
        const DialogueTTSStatus(
          turn: 2,
          speaker: 'Speaker 2',
          content: 'Test content 2',
          gender: '女',
          state: TTSState.ready,
          audioFilePath: '/test/path2.mp3',
        ),
      ];

      audioManager.setPlayQueue(testQueue);

      expect(audioManager.playQueue.length, equals(2));
      expect(audioManager.currentIndex, equals(-1));
      expect(audioManager.hasNext, isTrue);
      expect(audioManager.hasPrevious, isFalse);
    });

    test('should format duration correctly', () {
      const duration = Duration(minutes: 2, seconds: 30);
      final formatted = audioManager.formatDuration(duration);
      expect(formatted, equals('02:30'));
    });

    test('should calculate progress correctly', () {
      // 由于duration和position是只读属性，我们只能测试计算公式
      const duration = Duration(seconds: 100);
      const position = Duration(seconds: 25);
      final expectedProgress = position.inMilliseconds / duration.inMilliseconds;

      expect(expectedProgress, equals(0.25));
    });
  });

  group('Dialogue TTS Service Tests', () {
    late DialogueTTSService dialogueTTSService;
    late TTSApiService mockTTSApiService;
    late AudioPlayerManager mockAudioManager;

    setUp(() {
      mockTTSApiService = TTSApiService();
      mockAudioManager = AudioPlayerManager();
      dialogueTTSService = DialogueTTSService(
        ttsApiService: mockTTSApiService,
        audioPlayerManager: mockAudioManager,
      );
    });

    tearDown(() {
      mockAudioManager.dispose();
    });

    test('should initialize with empty state', () {
      expect(dialogueTTSService.dialogueStatuses.isEmpty, isTrue);
      expect(dialogueTTSService.speakerVoiceMap.isEmpty, isTrue);
      expect(dialogueTTSService.generatedCount, equals(0));
      expect(dialogueTTSService.totalCount, equals(0));
      expect(dialogueTTSService.isGenerating, isFalse);
      expect(dialogueTTSService.progress, equals(0.0));
    });

    test('should assign voices to participants correctly', () {
      final testDialogueData = {
        'participants': [
          {
            'name': 'John Doe',
            'gender': '男',
            'role_id': '主持人',
          },
          {
            'name': 'Jane Smith',
            'gender': '女',
            'role_id': '专家A',
          },
        ],
        'dialogue_flow': [
          {
            'turn': 1,
            'speaker': 'John Doe(主持人)',
            'content': 'Welcome to the discussion.',
          },
          {
            'turn': 2,
            'speaker': 'Jane Smith',
            'content': 'Thank you for having me.',
          },
        ],
      };

      // 这个测试需要模拟实际的API调用，在实际项目中应该使用mock
      // 这里只测试数据结构的正确性
      expect(testDialogueData['participants'], isNotNull);
      expect(testDialogueData['dialogue_flow'], isNotNull);
      
      final participants = testDialogueData['participants'] as List;
      expect(participants.length, equals(2));
      
      final dialogueFlow = testDialogueData['dialogue_flow'] as List;
      expect(dialogueFlow.length, equals(2));
    });

    test('should get generation stats correctly', () {
      final stats = dialogueTTSService.getGenerationStats();
      
      expect(stats.containsKey('total'), isTrue);
      expect(stats.containsKey('pending'), isTrue);
      expect(stats.containsKey('generating'), isTrue);
      expect(stats.containsKey('ready'), isTrue);
      expect(stats.containsKey('error'), isTrue);
      
      expect(stats['total'], equals(0));
      expect(stats['pending'], equals(0));
      expect(stats['generating'], equals(0));
      expect(stats['ready'], equals(0));
      expect(stats['error'], equals(0));
    });
  });

  group('TTS Models Tests', () {
    test('should create DialogueTTSStatus correctly', () {
      const status = DialogueTTSStatus(
        turn: 1,
        speaker: 'Test Speaker',
        content: 'Test content',
        gender: '男',
        state: TTSState.pending,
      );

      expect(status.turn, equals(1));
      expect(status.speaker, equals('Test Speaker'));
      expect(status.content, equals('Test content'));
      expect(status.gender, equals('男'));
      expect(status.state, equals(TTSState.pending));
      expect(status.audioFilePath, isNull);
      expect(status.error, isNull);
      expect(status.voice, isNull);
    });

    test('should create TTSResult correctly', () {
      const result = TTSResult(
        audioData: [1, 2, 3, 4, 5],
        format: 'mp3',
        sampleRate: 32000,
        filePath: '/test/path.mp3',
      );

      expect(result.audioData, equals([1, 2, 3, 4, 5]));
      expect(result.format, equals('mp3'));
      expect(result.sampleRate, equals(32000));
      expect(result.filePath, equals('/test/path.mp3'));
    });

    test('should handle TTSState enum correctly', () {
      expect(TTSState.values.length, equals(6));
      expect(TTSState.values.contains(TTSState.pending), isTrue);
      expect(TTSState.values.contains(TTSState.generating), isTrue);
      expect(TTSState.values.contains(TTSState.ready), isTrue);
      expect(TTSState.values.contains(TTSState.playing), isTrue);
      expect(TTSState.values.contains(TTSState.completed), isTrue);
      expect(TTSState.values.contains(TTSState.error), isTrue);
    });

    test('should handle AudioPlaybackState enum correctly', () {
      expect(AudioPlaybackState.values.length, equals(7));
      expect(AudioPlaybackState.values.contains(AudioPlaybackState.idle), isTrue);
      expect(AudioPlaybackState.values.contains(AudioPlaybackState.loading), isTrue);
      expect(AudioPlaybackState.values.contains(AudioPlaybackState.playing), isTrue);
      expect(AudioPlaybackState.values.contains(AudioPlaybackState.paused), isTrue);
      expect(AudioPlaybackState.values.contains(AudioPlaybackState.stopped), isTrue);
      expect(AudioPlaybackState.values.contains(AudioPlaybackState.completed), isTrue);
      expect(AudioPlaybackState.values.contains(AudioPlaybackState.error), isTrue);
    });
  });

  group('Integration Tests', () {
    test('should handle complete TTS workflow', () {
      // 这是一个集成测试的框架，实际测试需要mock网络请求
      final ttsService = TTSApiService();
      final audioManager = AudioPlayerManager();
      final dialogueService = DialogueTTSService(
        ttsApiService: ttsService,
        audioPlayerManager: audioManager,
      );

      // 验证服务初始化
      expect(ttsService, isNotNull);
      expect(audioManager, isNotNull);
      expect(dialogueService, isNotNull);

      // 清理资源
      audioManager.dispose();
    });
  });
}
