import 'package:flutter_test/flutter_test.dart';
import 'package:round_table/data/models/chat_message.dart';
import 'package:round_table/presentation/pages/round_table_service.dart';

void main() {
  group('TTS Integration Tests', () {
    late RoundTableService roundTableService;

    setUp(() {
      roundTableService = RoundTableService();
    });

    test('should extract dialogue data from second message correctly', () {
      // 模拟圆桌会议消息
      final messages = [
        const ChatMessage(role: 'system', content: '会议开始'),
        const ChatMessage(
          role: 'assistant', 
          content: '''
{
  "meeting_metadata": {
    "theme": "测试主题",
    "participants_count": 2
  },
  "participants": [
    {
      "name": "张三",
      "gender": "男",
      "role_id": "主持人"
    },
    {
      "name": "李四",
      "gender": "女", 
      "role_id": "专家A"
    }
  ],
  "dialogue_flow": [
    {
      "turn": 1,
      "speaker": "张三(主持人)",
      "content": "欢迎大家参加今天的讨论"
    },
    {
      "turn": 2,
      "speaker": "李四",
      "content": "谢谢主持人，很高兴参与讨论"
    }
  ]
}
          '''
        ),
      ];

      // 验证第二条消息包含对话数据
      expect(messages.length, equals(2));
      expect(messages[1].content.contains('dialogue_flow'), isTrue);
      expect(messages[1].content.contains('participants'), isTrue);
    });

    test('should identify TTS content correctly', () {
      const messageWithTTS = ChatMessage(
        role: 'assistant',
        content: '{"dialogue_flow": [{"turn": 1, "speaker": "测试", "content": "测试内容"}]}'
      );

      const messageWithoutTTS = ChatMessage(
        role: 'assistant',
        content: '这是普通的聊天消息'
      );

      expect(messageWithTTS.content.contains('dialogue_flow'), isTrue);
      expect(messageWithoutTTS.content.contains('dialogue_flow'), isFalse);
    });

    test('should handle JSON parsing correctly', () {
      const jsonContent = '''
{
  "participants": [
    {"name": "张三", "gender": "男"},
    {"name": "李四", "gender": "女"}
  ],
  "dialogue_flow": [
    {"turn": 1, "speaker": "张三", "content": "测试内容"}
  ]
}
      ''';

      expect(() {
        // 这里应该能够成功解析JSON
        final data = jsonContent.trim();
        expect(data.startsWith('{'), isTrue);
        expect(data.endsWith('}'), isTrue);
      }, returnsNormally);
    });

    test('should extract gender from speaker correctly', () {
      const dialogueData = {
        'participants': [
          {'name': '张三', 'gender': '男'},
          {'name': '李四', 'gender': '女'},
        ]
      };

      // 模拟性别提取逻辑
      String extractGender(String speaker, Map<String, dynamic> data) {
        final participants = data['participants'] as List?;
        if (participants != null) {
          for (final participant in participants) {
            final name = participant['name'] as String?;
            if (name != null && speaker.contains(name)) {
              return participant['gender'] as String? ?? '男';
            }
          }
        }
        return '男';
      }

      expect(extractGender('张三(主持人)', dialogueData), equals('男'));
      expect(extractGender('李四', dialogueData), equals('女'));
      expect(extractGender('未知人员', dialogueData), equals('男')); // 默认值
    });

    test('should handle message initialization correctly', () {
      final initialMessages = [
        const ChatMessage(role: 'system', content: '系统消息'),
        const ChatMessage(
          role: 'assistant',
          content: '{"dialogue_flow": [{"turn": 1, "speaker": "测试", "content": "测试"}]}'
        ),
      ];

      // 验证初始化逻辑
      expect(initialMessages.isNotEmpty, isTrue);
      
      // 检查是否有TTS内容
      bool hasTTSContent = false;
      for (final message in initialMessages) {
        if (message.content.contains('dialogue_flow')) {
          hasTTSContent = true;
          break;
        }
      }
      
      expect(hasTTSContent, isTrue);
    });
  });

  group('Chat Message TTS Detection', () {
    test('should detect TTS content in various formats', () {
      const testCases = [
        // 标准JSON格式
        '{"dialogue_flow": []}',
        // 带空格的JSON
        ' { "dialogue_flow" : [] } ',
        // 嵌套在其他内容中
        '前言\n{"dialogue_flow": []}\n后续内容',
      ];

      for (final testCase in testCases) {
        expect(testCase.contains('dialogue_flow'), isTrue, 
               reason: 'Should detect dialogue_flow in: $testCase');
      }
    });

    test('should not detect TTS content in regular messages', () {
      const testCases = [
        '这是普通的聊天消息',
        '没有特殊格式的文本',
        '{"other_data": "value"}', // 不包含dialogue_flow
      ];

      for (final testCase in testCases) {
        expect(testCase.contains('dialogue_flow'), isFalse,
               reason: 'Should not detect dialogue_flow in: $testCase');
      }
    });
  });
}
