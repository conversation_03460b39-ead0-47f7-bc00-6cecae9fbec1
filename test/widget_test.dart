import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:round_table/main.dart';
import 'package:round_table/core/services/storage_service.dart';

void main() {
  group('Main App Tests', () {
    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      WidgetsFlutterBinding.ensureInitialized();
      await StorageService.init();
    });

    testWidgets('App starts without errors', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // 基本验证：应用能够启动
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('Bottom navigation exists', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // 验证底部导航栏存在
      expect(find.byType(BottomNavigationBar), findsOneWidget);
    });
  });
}
