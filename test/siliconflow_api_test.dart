import 'package:flutter_test/flutter_test.dart';

import 'package:round_table/data/models/chat_message.dart';
import 'package:round_table/data/services/siliconflow_api_service.dart';
import 'package:round_table/data/repositories/chat_repository.dart';
import 'package:round_table/core/network/api_exception.dart';
import 'package:round_table/core/services/system_prompt_service.dart';

void main() {
  group('SiliconFlow API Tests', () {
    test('ChatMessage model creation', () {
      const message = ChatMessage(
        role: 'user',
        content: 'Hello, AI!',
      );

      expect(message.role, 'user');
      expect(message.content, 'Hello, AI!');
    });

    test('ChatCompletionRequest model creation', () {
      final request = ChatCompletionRequest(
        model: 'Qwen/Qwen3-8B',
        messages: const [
          ChatMessage(role: 'user', content: 'Hello'),
        ],
        maxTokens: 2048,
        thinkingBudget: 1024,
      );

      expect(request.model, 'Qwen/Qwen3-8B');
      expect(request.messages.length, 1);
      expect(request.maxTokens, 2048);
      expect(request.thinkingBudget, 1024);
      expect(request.stream, false);
    });

    test('ChatCompletionResponse model parsing', () {
      final json = {
        'id': 'chatcmpl-123',
        'object': 'chat.completion',
        'created': 1234567890,
        'model': 'Qwen/Qwen3-8B',
        'choices': [
          {
            'index': 0,
            'message': {
              'role': 'assistant',
              'content': 'Hello! How can I help you?',
            },
            'finish_reason': 'stop',
          },
        ],
      };

      final response = ChatCompletionResponse.fromJson(json);

      expect(response.id, 'chatcmpl-123');
      expect(response.object, 'chat.completion');
      expect(response.model, 'Qwen/Qwen3-8B');
      expect(response.choices.length, 1);
      expect(response.choices[0].message.content, 'Hello! How can I help you?');
    });

    test('StreamingDelta model parsing', () {
      final json = {
        'role': 'assistant',
        'content': 'Hello',
        'reasoningContent': 'Let me think',
      };

      final delta = StreamingDelta.fromJson(json);

      expect(delta.role, 'assistant');
      expect(delta.content, 'Hello');
      expect(delta.reasoningContent, 'Let me think');
    });

    test('API Exception hierarchy', () {
      final networkError = NetworkError();
      expect(networkError.message, '网络连接失败，请检查网络设置');

      final serverError = ServerError(statusCode: 500);
      expect(serverError.message, '服务器内部错误，请稍后重试');
      expect(serverError.statusCode, 500);

      final unauthorizedError = UnauthorizedError();
      expect(unauthorizedError.statusCode, 401);
      expect(unauthorizedError.message, '认证失败，请重新登录');

      final timeoutError = TimeoutError();
      expect(timeoutError.message, '请求超时，请重试');

      final unknownError = UnknownError(message: 'Custom error');
      expect(unknownError.message, 'Custom error');
    });
  });

  group('ChatRepository Tests', () {
    test('ChatRepository can be instantiated', () {
      final apiService = SiliconFlowApiService();
      final systemPromptService = SystemPromptService();
      final chatRepository = ChatRepository(apiService, systemPromptService);

      expect(chatRepository, isA<ChatRepository>());
    });

    test('setApiKey method exists', () {
      final apiService = SiliconFlowApiService();
      final systemPromptService = SystemPromptService();
      final chatRepository = ChatRepository(apiService, systemPromptService);

      // 这个方法应该存在且不抛出异常
      expect(() => chatRepository.setApiKey('test-api-key'), returnsNormally);
    });
  });
}
